import ContentLoadingScreen from "@/components/shared/LoadingScreen/ContentLoadingScreen";
import { useSelector } from "react-redux";
import { useSearchParams } from "react-router-dom";
import TableHead from "./TableHead";
import PaginationComponent from "@/components/shared/Pagination/PaginationComponent";

import TableData from "./TableData";
import PaymentModal from "../../../../shared/PaymentModal/PaymentModal";
import { useGetAllInstallmentsQuery } from "@/redux/services/contractApiSlice";
import { roundTo2 } from "@/util/formatters";

function InstallmentTable() {
    const [searchParams] = useSearchParams();

    const limit = parseInt(searchParams.get("limit") || "16", 10);
    const offset = parseInt(searchParams.get("offset") || "0", 10);

    const queryObject = {
        ...Object.fromEntries(searchParams.entries()),
        limit,
        offset,
    };

    const { data: installments, isLoading, isError, error, refetch } = useGetAllInstallmentsQuery(queryObject);

    const { isSearchOpen } = useSelector(state => state.layout);
    const { isModalOpen, contractInstallment, params } = useSelector(state => state.contractPayment);


    if (isError && error.originalStatus == 404) return <p className="text-center my-[40px]">Məlumat tapılmadı</p>
    if (isError) return <p className="errorText">Bir xəta baş verdi xaiş edirik bir daha cəhd edin. Error: {error?.originalStatus || error?.status}</p>

    return (
        <div className="pb-8 pt-[24px] w-full">
            <div className={`w-[calc(100%-70px)] sm:w-[calc(100%-90px)] overflow-auto rounded-[20px] h-[calc(100vh-220px)] transition-all duration-300 ease-in-out ${isSearchOpen ? 'w-[calc(100%-80px)] sm:w-[calc(100%-320px)]' : ''}`}>
                {isLoading ? (
                    <ContentLoadingScreen />
                ) : (
                    installments
                        && installments.results
                        && installments.results.data
                        && installments.results.data.length > 0 ?
                        <>
                            <PaymentModal
                                isOpen={isModalOpen}
                                contractInstallment={contractInstallment}
                                refetch={refetch}
                                params={params}
                            />
                            <table className="w-full border-2 border-table-border border-separate border-spacing-0 rounded-[20px]">
                                <TableHead />
                                <tbody className="[&_td]:py-[10px] [&_td]:px-2 [&_td]:border-b-2 [&_td]:border-table-border [&_td]:text-sm [&_td]:whitespace-nowrap [&_tr:last-child_td]:border-b-0 [&_tr_td:first-child]:pl-[13px] [&_tr_td:last-child]:pr-[13px] [&_tr]:border-t-2 [&_tr]:border-b-2 [&_tr]:border-table-border">
                                    {installments.results.data.map(installment => <TableData key={installment.id} installment={installment} />)}
                                </tbody>
                                <tfoot className="sticky bottom-0 h-10 bg-secondary [&_td]:py-[10px] [&_td]:px-2 [&_td]:leading-5 [&_td]:text-sm [&_td]:whitespace-nowrap [&_td]:text-white [&_td:first-child]:rounded-bl-[18px] [&_td:first-child]:pl-[13px] [&_td:last-child]:rounded-br-[18px]">
                                    <tr>
                                        <td>Sıralanır: {installments.count}</td>
                                        <td colSpan={4}></td>
                                        <td>{roundTo2(Number(installments?.results?.installment_summaries?.total_remaining_debt ?? 0))}</td>
                                        <td colSpan={2}></td>
                                    </tr>
                                </tfoot>
                            </table>
                        </>
                        : <p className="text-center my-[40px]">Məlumat tapılmadı</p>
                )}
            </div>
            {installments
                && !isLoading
                && <PaginationComponent totalCount={installments.count} />}
        </div>
    )
}

export default InstallmentTable
import { TextField } from "@mui/material"
import CitySelector from "@/components/shared/CitySelector/CitySelector"
import classes from "./profilestyles.module.css";
import { useSelector } from "react-redux";
import { useForm, FormProvider } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { profileSchema } from "@/util/schemas/profileSchema";
import { Helmet } from "react-helmet-async";
import { useGetUserProfileQuery, useUpdateUserProfileMutation } from "@/redux/services/userApiSlice";
import { objectFormatter } from "@/util/formatters";
import { errorFinder } from "@/util/errorHandler";
import { showToast } from "@/util/showToast";
import PhoneInput from "@/components/shared/Phone/PhoneInput";

function UserProfile() {
    const { data: userProfile = {}, refetch } = useGetUserProfileQuery();
    const [updateProfile, { isLoading }] = useUpdateUserProfileMutation();
    const selectedCity = useSelector((state) => state.city.selectedCity);

    const methods = useForm({
        mode: "onTouched",
        resolver: yupResolver(profileSchema),
        defaultValues: {
            phone1: userProfile.phone1 || "",
            phone2: userProfile.phone2 || "",
            email: userProfile.email || "",
            city: userProfile.city?.id || ""
        }
    });

    const {
        handleSubmit,
        formState: { errors },
        clearErrors,
        control,
        register,
    } = methods;

    const updatedUserProfile = { ...userProfile, city: userProfile.city?.id };

    async function onSubmit(data) {
        console.log(data);

        try {
            data.city_id = selectedCity?.id || null;
            data.phone2 = data.phone2 || null;

            const formattedData = objectFormatter(data);

            await updateProfile(formattedData).unwrap();
            await refetch();

            showToast("İstifadəçi məlumatları uğurla yeniləndi", "success");
        } catch (err) {
            console.error(err);
            const msg = errorFinder(err);
            showToast(msg, "error");
        }
    }


    return (
        <>
            <Helmet>
                <title>inKredo | İstifadəçi Məlumatları</title>
            </Helmet>
            {userProfile &&
                <div className="mt-5 w-full sm:mr-5">
                    <div className="blueBanner bg-[#073763] h-[192px] w-full rounded-tl-[20px] rounded-tr-[20px]"></div>
                    <div className="w-[80%] mx-auto">
                        <div className="my-[23px]">
                            <h2 className={classes.fullnameStyles}>{userProfile.fullname}</h2>
                            <p className={classes.emailStyles}>{userProfile.email}</p>
                        </div>
                        <FormProvider {...methods}>
                            <form onSubmit={handleSubmit(onSubmit)}>
                                <div className="flex items-start gap-10 mb-8">
                                    <div className="flex-grow flex-shrink basis-[100px]">
                                        <PhoneInput
                                            control={control}
                                            name="phone1"
                                            defaultValue={userProfile?.phone1 || ""}
                                            errors={errors}
                                            onChange={() => clearErrors("phone1")}
                                        />
                                    </div>
                                    <div className="flex-grow flex-shrink basis-[100px] pt-[3px]">
                                        <CitySelector
                                            isLabelAvailableForControlled={true}
                                            isSearch={false}
                                            user={updatedUserProfile}
                                        />
                                    </div>
                                </div>
                                <div className="flex items-start gap-10">
                                    <div className="flex-grow flex-shrink basis-[100px] pt-[3px]">
                                        <PhoneInput
                                            control={control}
                                            name="phone2"
                                            errors={errors}
                                            defaultValue={userProfile?.phone2 || ""}
                                            onChange={() => clearErrors("phone2")}
                                        />
                                    </div>
                                    <div className="flex-grow flex-shrink basis-[100px]">
                                        <TextField
                                            {...register("email")}
                                            onChange={() => clearErrors("email")}
                                            className="w-full"
                                            label="Elektron poçt ünvanı"
                                            variant="standard"
                                            // type="email"
                                            error={!!errors?.email}
                                            helperText={errors?.email?.message}
                                        />
                                    </div>
                                </div>
                                <div className="w-full flex justify-center mt-10">
                                    <button
                                        type="submit"
                                        disabled={isLoading}
                                        className={classes.submitButton}
                                    >
                                        {isLoading ? "Yenilənir..." : "Yenilə"}
                                    </button>
                                </div>
                            </form>
                        </FormProvider>
                    </div>
                </div>}
        </>
    )
}

export default UserProfile
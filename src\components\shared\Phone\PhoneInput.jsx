import PhoneInputComponent from "react-phone-number-input";
import { Controller } from "react-hook-form";
import "react-phone-number-input/style.css";

function PhoneInput({
    control,
    name,
    defaultValue = "",
    value,
    onChange,
    errors = {},
    className = "",
    ...props
}) {
    const errorMsg = errors[name]?.message;

    const inputClasses = `
    border-b
    ${errorMsg ? "border-red-500" : "border-gray-400"}
    focus:border-blue-500
    !outline-none
    ${className}
  `;

    if (control) {
        return (
            <Controller
                name={name}
                control={control}
                defaultValue={defaultValue}
                render={({ field }) => (
                    <div className="flex flex-col w-full">
                        <PhoneInputComponent
                            {...field}
                            defaultCountry="AZ"
                            international
                            countryCallingCodeEditable={false}
                            className={inputClasses}
                            onChange={(val) => {
                                field.onChange(val);
                                onChange?.(val);
                            }}
                            {...props}
                        />
                        {errorMsg && (
                            <span className="text-red-500 text-sm mt-1">{errorMsg}</span>
                        )}
                    </div>
                )}
            />
        );
    }

    return (
        <div className="flex flex-col w-full">
            <PhoneInputComponent
                value={value}
                defaultCountry="AZ"
                international
                countryCallingCodeEditable={false}
                className={inputClasses}
                onChange={onChange}
                {...props}
            />
            {errorMsg && (
                <span className="text-red-500 text-sm mt-1">{errorMsg}</span>
            )}
        </div>
    );
}

export default PhoneInput;

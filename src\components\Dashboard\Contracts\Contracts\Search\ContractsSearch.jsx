import SearchComponent from "@/components/shared/Search/SearchComponent";
import { contractStatusTypes, paymentStatusTypes } from "@/util/constants";

function ContractsSearch() {
    const searchFields = [
        {
            type: 'text',
            name: 'customer_fullname',
            label: '<PERSON><PERSON>ş<PERSON>əri axtar',
        },
        {
            type: 'city',
            name: 'customer__city',
            label: '<PERSON>əhər adı seç',
        },
        {
            type: 'text',
            name: 'guarantor_fullname',
            label: '<PERSON>ə<PERSON><PERSON>',
        },
        {
            type: 'text',
            name: 'executor_fullname',
            label: 'İ<PERSON>raçı',
        },
        {
            type: 'select',
            name: 'payment_status',
            label: 'Ödəmə Statusu',
            options: paymentStatusTypes,
        },
        {
            type: 'select',
            name: 'status',
            label: 'Müqavilə Statusu',
            options: contractStatusTypes,
        },
        {
            type: 'date',
            name: 'date',
            label: 'Tarix',
            fieldProps: { className: 'mt-[20px] mb-8' }
        }
    ];

    const defaultValues = {
        customer_fullname: '',
        customer__city: '',
        guarantor_fullname: '',
        executor_fullname: '',
        payment_status: '',
        payment_status_display: { name: "<PERSON><PERSON>sı", value: "" },
        status: '',
        status_display: { name: "Hamısı", value: "" },
        date: [null, null]
    };

    return (
        <div>
            <SearchComponent
                fields={searchFields}
                defaultValues={defaultValues}
                limit="13"
            />
        </div>
    );
}

export default ContractsSearch;

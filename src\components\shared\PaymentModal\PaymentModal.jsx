import { <PERSON><PERSON>, DialogContent, TextField } from "@mui/material";

import { modalStyles } from "@/styles/shared/modalStyles";
import { useDispatch, useSelector } from "react-redux";
import { handleModal } from "@/redux/features/modal/contractPaymentSlice";
import { useEffect, useRef, useState } from "react";

import { usePayInstallationMutation } from "@/redux/services/contractApiSlice.js";
import { errorFinder } from "@/util/errorHandler.js";
import { objectFormatter } from "@/util/formatters.js";
import { showToast } from "@/util/showToast.js";
import { formatToDMY, getTodayDateTimeLocalString } from "@/util/dateHelpers";
import { Close } from "@mui/icons-material";

export default function PaymentModal({ isOpen = false }) {
  const { modalData: { installment, amount } } = useSelector(state => state.contractPayment);
  const [localAmount, setLocalAmount] = useState(amount);
  const [fileName, setFileName] = useState(null);

  useEffect(() => {
    setLocalAmount(amount);
  }, [amount, isOpen]);

  console.log(amount, localAmount);

  const [payInstallation, { isLoading }] = usePayInstallationMutation();
  const dispatch = useDispatch();

  async function handleSubmit(e) {
    e.preventDefault();

    if (localAmount < 1) {
      return;
    }

    try {
      const fd = new FormData(e.target);
      await handlePayment(fd);
      dispatch(handleModal());

      fileInputRef.current.value = null;
      setFileName(null);
    } catch (error) {
      console.log(error);

      const msg = errorFinder(error);

      showToast(msg, "error");
    }
  }

  const handlePayment = async (fd) => {
    const currentDate = fd.get("paid_date");
    const receipt = fd.get("receipt");
    const [date, time] = currentDate.split("T");

    const updatedDate = `${formatToDMY(date)} ${time}`;

    const data = {
      amount: localAmount,
      note: fd.get("note"),
      installment: installment,
      paid_date: updatedDate,
      receipt: receipt && receipt.name ? receipt : null,
    };
    console.log(data);

    await payInstallation(objectFormatter(data)).unwrap();
  };

  const fileInputRef = useRef(null);

  return (
    <Dialog
      sx={modalStyles.payment.sxstyle}
      slotProps={modalStyles.payment.slotprops}
      open={isOpen}
      onClose={(_, reason) => {
        if (reason === "backdropClick" || reason === "escapeKeyDown") {
          dispatch(handleModal());
          fileInputRef.current.value = null;
          setFileName(null);
        }
      }}
    >
      <DialogContent>
        <form onSubmit={handleSubmit} className="w-full">
          <h2 className={`modalTitle mb-[30px]`}>Cari Ay Ödənişi</h2>
          <div className="flex w-full justify-between mb-[30px] items-center">
            <span className="w-1/3 text-[14px]">Məbləğ</span>
            <TextField
              name="amount"
              className="w-2/3"
              variant="standard"
              value={localAmount || ""}
              type="number"
              onChange={(e) => setLocalAmount(e.target.value)}
              inputProps={{ step: "any" }}
              error={localAmount < 1}
              helperText={localAmount < 1 ? `Minimum məbləğ 1 AZN-dir.` : ""}
            />
          </div>
          <div className="flex w-full justify-between mb-[30px] items-center">
            <span className="w-1/3 text-[14px]">Tarix</span>
            <input defaultValue={getTodayDateTimeLocalString()} className="w-2/3 border-b-1 border-black outline-none text-[14px]" type="datetime-local" name="paid_date" id="paid_date" />
          </div>
          <div className="flex w-full justify-between mb-[30px] items-center">
            <span className="w-1/3 text-[14px]">Sənəd</span>
            <div className="w-2/3 flex items-center border-b-1 relative text-[14px]">
              <label htmlFor="receipt" className="cursor-pointer w-5/6 truncate">
                {fileName || "Fayl seçin..."}
              </label>

              {fileName && (
                <Close
                  fontSize="small"
                  className="absolute right-0 top-1/2 -translate-y-1/2 text-gray-500 cursor-pointer"
                  onClick={() => {
                    fileInputRef.current.value = null;
                    setFileName(null);
                  }}
                />
              )}

              <input
                ref={fileInputRef}
                type="file"
                id="receipt"
                name="receipt"
                accept="image/*,application/pdf"
                className="hidden"
                onChange={(e) => {
                  const file = e.target.files[0];
                  if (file) {
                    setFileName(file.name);
                  } else {
                    setFileName(null);
                  }
                }}
              />
            </div>
          </div>

          <div className="mb-[30px]">
            <TextField
              id="outlined-multiline-static"
              label="Qeyd"
              name="note"
              multiline
              minRows={4}
              className="w-full"
              variant="outlined"
            />
          </div>
          <div className="flex gap-[28px] justify-end">
            <button type="button" onClick={() => dispatch(handleModal())} className="def-button def-button-gray">Ləğv et</button>
            <button disabled={isLoading} className="def-button def-button-success">{isLoading ? "Ödəniş Edilir" : "Ödəniş Et"}</button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}

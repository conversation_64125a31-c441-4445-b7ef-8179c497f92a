import generalTableStyles from "@/styles/shared/generalTableStyles.module.css"
import PaginationComponent from "@/components/shared/Pagination/PaginationComponent";
import TableHead from "@/components/Dashboard/HumanResources/Payrolls/Table/TableHead";
import TableData from "@/components/Dashboard/HumanResources/Payrolls/Table/TableData";
import { useSearchParams } from "react-router-dom";
import { useSelector } from "react-redux";
import ContentLoadingScreen from "@/components/shared/LoadingScreen/ContentLoadingScreen";
import { useGetAllPayrollsQuery } from "@/redux/services/userApiSlice";
import { useState } from "react";
import Buttons from "@/components/Dashboard/HumanResources/Payrolls/Actions/Buttons";

function PayrollsPage() {
  const [searchParams] = useSearchParams();

  const [selectedIds, setSelectedIds] = useState([]);

  const limit = parseInt(searchParams.get("limit") || "16", 10);
  const offset = parseInt(searchParams.get("offset") || "0", 10);

  const queryObject = {
    ...Object.fromEntries(searchParams.entries()),
    limit,
    offset,
  };

  const { data: payrolls, isLoading, isError, error } = useGetAllPayrollsQuery(queryObject);

  const { isSearchOpen } = useSelector(state => state.layout);

  function handleSelectedIds(type = "update", id) {
    switch (type) {
      case "update": {
        setSelectedIds(prev => prev.includes(id) ? prev.filter(i => i !== id) : [...prev, id]);
        break;
      }
      case "unSelectAll": {
        setSelectedIds([]);
        break;
      }
      case "selectAll": {
        setSelectedIds(payrolls.results.map(p => p.id));
        break;
      }
    }
  }

  if (isError) return <p className="errorText">Bir xəta baş verdi xaiş edirik bir daha cəhd edin. Error: {error?.originalStatus || error?.status}</p>

  return (
    <div className="pb-8 pt-[24px] w-full">
      <div className="mb-[24px] gap-[28px] flex flex-col items-start md:flex-row md:items-center">
        <Buttons handleSelectedIds={handleSelectedIds} selectedIds={selectedIds} />
      </div>

      <div className={`${generalTableStyles.tableWrapper} ${isSearchOpen && generalTableStyles.searchOpened} h-[calc(100vh-280px)]`}
      >
        {isLoading ? (
          <ContentLoadingScreen />
        ) : (
          payrolls
            && payrolls.results
            && payrolls.results.length > 0 ?
            <table className={generalTableStyles.table}>
              <TableHead />
              <tbody className={generalTableStyles.tbody}>
                {payrolls.results.map(payroll => <TableData key={payroll.id} payroll={payroll} handleSelectedIds={handleSelectedIds} />)}
              </tbody>
              <tfoot className={generalTableStyles.tfoot}>
                <tr>
                  <td>Sıralanır: {payrolls.count}</td>
                </tr>
              </tfoot>
            </table>
            : <p className="text-center my-[40px]">Məlumat tapılmadı</p>
        )}
      </div>
      {payrolls
        && !isLoading
        && <PaginationComponent totalCount={payrolls.count} />}
    </div>
  )
}

export default PayrollsPage
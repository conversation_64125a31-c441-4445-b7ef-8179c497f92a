
import PaginationComponent from "@/components/shared/Pagination/PaginationComponent";
import TableHead from "@/components/Dashboard/HumanResources/Payrolls/Table/TableHead";
import TableData from "@/components/Dashboard/HumanResources/Payrolls/Table/TableData";
import { useSearchParams } from "react-router-dom";
import { useSelector } from "react-redux";
import ContentLoadingScreen from "@/components/shared/LoadingScreen/ContentLoadingScreen";
import { useGetAllPayrollsQuery } from "@/redux/services/userApiSlice";
import { useState } from "react";
import Buttons from "@/components/Dashboard/HumanResources/Payrolls/Actions/Buttons";

function PayrollsPage() {
  const [searchParams] = useSearchParams();

  const [selectedIds, setSelectedIds] = useState([]);

  const limit = parseInt(searchParams.get("limit") || "16", 10);
  const offset = parseInt(searchParams.get("offset") || "0", 10);

  const queryObject = {
    ...Object.fromEntries(searchParams.entries()),
    limit,
    offset,
  };

  const { data: payrolls, isLoading, isError, error } = useGetAllPayrollsQuery(queryObject);

  const { isSearchOpen } = useSelector(state => state.layout);

  function handleSelectedIds(type = "update", id) {
    switch (type) {
      case "update": {
        setSelectedIds(prev => prev.includes(id) ? prev.filter(i => i !== id) : [...prev, id]);
        break;
      }
      case "unSelectAll": {
        setSelectedIds([]);
        break;
      }
      case "selectAll": {
        setSelectedIds(payrolls.results.map(p => p.id));
        break;
      }
    }
  }

  if (isError) return <p className="errorText">Bir xəta baş verdi xaiş edirik bir daha cəhd edin. Error: {error?.originalStatus || error?.status}</p>

  return (
    <div className="pb-8 pt-[24px] w-full">
      <div className="mb-[24px] gap-[28px] flex flex-col items-start md:flex-row md:items-center">
        <Buttons handleSelectedIds={handleSelectedIds} selectedIds={selectedIds} />
      </div>

      <div className={`w-[calc(100%-70px)] sm:w-[calc(100%-90px)] overflow-auto rounded-[20px] h-[calc(100vh-280px)] transition-all duration-300 ease-in-out ${isSearchOpen ? 'w-[calc(100%-80px)] sm:w-[calc(100%-320px)]' : ''}`}
      >
        {isLoading ? (
          <ContentLoadingScreen />
        ) : (
          payrolls
            && payrolls.results
            && payrolls.results.length > 0 ?
            <table className="w-full border-2 border-table-border border-separate border-spacing-0 rounded-[20px]">
              <TableHead />
              <tbody className="[&_td]:py-[10px] [&_td]:px-2 [&_td]:border-b-2 [&_td]:border-table-border [&_td]:text-sm [&_td]:whitespace-nowrap [&_tr:last-child_td]:border-b-0 [&_tr_td:first-child]:pl-[13px] [&_tr_td:last-child]:pr-[13px] [&_tr]:border-t-2 [&_tr]:border-b-2 [&_tr]:border-table-border">
                {payrolls.results.map(payroll => <TableData key={payroll.id} payroll={payroll} handleSelectedIds={handleSelectedIds} />)}
              </tbody>
              <tfoot className="sticky bottom-0 h-10 bg-secondary [&_td]:py-[10px] [&_td]:px-2 [&_td]:leading-5 [&_td]:text-sm [&_td]:whitespace-nowrap [&_td]:text-white [&_td:first-child]:rounded-bl-[18px] [&_td:first-child]:pl-[13px] [&_td:last-child]:rounded-br-[18px]">
                <tr>
                  <td>Sıralanır: {payrolls.count}</td>
                </tr>
              </tfoot>
            </table>
            : <p className="text-center my-[40px]">Məlumat tapılmadı</p>
        )}
      </div>
      {payrolls
        && !isLoading
        && <PaginationComponent totalCount={payrolls.count} />}
    </div>
  )
}

export default PayrollsPage
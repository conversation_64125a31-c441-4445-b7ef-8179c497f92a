import PhoneInput from "@/components/shared/Phone/PhoneInput";
import { useFormContext } from "react-hook-form";

export const PhoneSection = ({ formKey, admin }) => {
    const { control, formState: { errors }, clearErrors } = useFormContext();
    return (
        <>
            <div className="flex items-center justify-between mb-[27px]">
                <label className="text-[14px] font-[500]">Tel nömrəsi 1 *</label>
                <div className="w-1/2 flex flex-col">
                    <PhoneInput
                        key={`phone1-${formKey}`}
                        control={control}
                        name="phone1"
                        defaultValue={admin?.phone1 || ""}
                        errors={errors}
                        onChange={() => clearErrors("phone1")}
                    />
                </div>
            </div>
            <div className="flex items-center justify-between my-[27px]">
                <label className="text-[14px] font-[500]">Tel nömrəsi 2</label>
                <div className="w-1/2 flex flex-col">
                    <PhoneInput
                        key={`phone2-${formKey}`}
                        control={control}
                        name="phone2"
                        defaultValue={admin?.phone2 || ""}
                        errors={errors}
                        onChange={() => clearErrors("phone2")}
                    />
                </div>
            </div>
        </>
    );
};
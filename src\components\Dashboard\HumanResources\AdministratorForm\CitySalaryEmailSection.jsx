import CitySelector from "@/components/shared/CitySelector/CitySelector";
import { TextField } from "@mui/material";
import { useFormContext } from "react-hook-form";

export const CitySalaryEmailSection = ({ formKey, admin }) => {
    const { control, formState: { errors }, register, clearErrors } = useFormContext();

    return (
        <>
            <div className="flex items-center justify-between my-[27px]">
                <label className="text-[14px] font-[500]">Şəhər *</label>
                <div className="w-1/2 flex flex-col">
                    <CitySelector
                        user={{ city: admin?.city?.id }}
                        width="w-full"
                        errors={errors}
                        control={control}
                        key={`city-${formKey}`}
                    />
                </div>
            </div>
            <div className="flex items-center justify-between my-[27px]">
                <label className="text-[14px] font-[500]"><PERSON><PERSON><PERSON>kha<PERSON>qı</label>
                <div className="w-1/2 flex flex-col">
                    <TextField
                        key={`salary-${formKey}`}
                        type="number"
                        variant="standard"
                        fullWidth
                        {...register("salary")}
                        defaultValue={admin?.salary || ""}
                        error={!!errors.salary}
                        helperText={errors.salary?.message}
                        onChange={() => clearErrors("salary")}
                    />
                </div>
            </div>
            <div className="flex items-center justify-between my-[27px]">
                <label className="text-[14px] font-[500]">Email *</label>
                <div className="w-1/2 flex flex-col">
                    <TextField
                        key={`email-${formKey}`}
                        type="email"
                        variant="standard"
                        fullWidth
                        {...register("email")}
                        defaultValue={admin?.email || ""}
                        placeholder="nümunə@gmail.com"
                        error={!!errors.email}
                        helperText={errors.email?.message}
                        onChange={() => clearErrors("email")}
                    />
                </div>
            </div>
        </>
    );
};
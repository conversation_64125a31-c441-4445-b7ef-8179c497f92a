import { Helmet } from "react-helmet-async";
import SalesChart from "@/components/Dashboard/Review/SalesChart";
import CostsChart from "@/components/Dashboard/Review/CostsChart";
import InvestmentsChart from "@/components/Dashboard/Review/InvestmentsChart";
import InstallmentsChart from "@/components/Dashboard/Review/InstallmentsChart";
// import DelaysChart from "@/components/Dashboard/Review/DelaysChart";
import IncomesChart from "@/components/Dashboard/Review/IncomesChart";



function ReviewPage() {
  return (
    <>
      <Helmet>
        <title>inKredo | Ana Səhifə</title>
      </Helmet>
      <div className="py-6 pr-6 w-full h-[calc(100vh - 24px)] grid grid-cols-1 gap-6 lg:grid-cols-2 xl:grid-cols-3">
        <SalesChart />
        <CostsChart />
        <InvestmentsChart />
        <InstallmentsChart />
        {/* <DelaysChart /> */}
        <IncomesChart />
      </div>
    </>
  )
}

export default ReviewPage
import CloseIcon from "@mui/icons-material/Close";
import { useFormContext } from "react-hook-form";

export const ProfileImageSection = ({ imagePreview, fileInputRef, handleImageChange, clearImage }) => {
    const { formState: { errors } } = useFormContext();

    return (
        <div className="flex items-center justify-between gap-4 my-[27px]">
            <label className="text-[14px] font-[500]">İstifadəçi şəkli *</label>
            <div className="w-1/2 flex flex-col gap-2">
                <label className="cursor-pointer inline-block border border-black text-sm px-4 py-2 rounded">
                    {fileInputRef.current?.files?.[0] ? `Seçilmiş: ${fileInputRef.current.files[0].name}` : "Şəkil seçin"}
                    <input
                        type="file"
                        accept="image/*"
                        ref={fileInputRef}
                        onChange={handleImageChange}
                        className="hidden"
                    />
                </label>
                {errors.profileImage?.message && <p className="text-sm text-red-500">{errors.profileImage.message}</p>}
                {imagePreview && (
                    <div className="relative w-fit mt-2">
                        <img src={imagePreview} alt="İstifadəçi şəkli" className="w-32 h-32 object-cover rounded border" />
                        <button type="button" onClick={clearImage} className="absolute -top-2 -right-2 bg-white text-red-500 rounded-full hover:text-red-700">
                            <CloseIcon fontSize="small" />
                        </button>
                    </div>
                )}
            </div>
        </div>
    );
};

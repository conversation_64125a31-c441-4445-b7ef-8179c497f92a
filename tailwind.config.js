/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        'main': '#073763',
        'secondary': '#336491',
        'third': '#92a1ca',
        'success': '#21d25c',
        'clearall': '#efbf3a',
        'fullpayment': '#ccebcc',
        'incompletepayment': '#fae9b0',
        'latepayment': '#F1B6B6',
        'gray-custom': '#bdbdbd',
        'modal-input-border': '#303030',
        'modal-placeholder-border': '#858585',
        'cell-selected': '#C4C6CC',
        'red-custom': '#CC1212',
        'table-border': 'rgba(209, 209, 209, 0.31)',
        'all-paid-contracts': '#EEB0FA',
        'all-paid-installments': '#808080',
        'over-payment': '#BDCDF8',
      },
      lineHeight: {
        '19.07': '19.07px',
        '21.78': '21.78px',
        '16.34': '16.34px',
      },
    },
  },
  plugins: [
    function({ addUtilities }) {
      const newUtilities = {
        '.def-button': {
          color: 'white',
          fontSize: '14px',
          lineHeight: '19.07px',
          boxShadow: '-1px 1px 2px 0px #00000080',
          padding: '9px 30px',
          borderRadius: '32px',
          display: 'inline-block',
          cursor: 'pointer',
          '&:disabled': {
            opacity: '0.5',
            cursor: 'not-allowed',
          },
        },
        '.def-button-main': {
          backgroundColor: '#073763',
        },
        '.def-button-secondary': {
          backgroundColor: '#336491',
        },
        '.def-button-success': {
          backgroundColor: '#21d25c',
        },
        '.def-button-yellow': {
          backgroundColor: '#efbf3a',
        },
        '.def-button-gray': {
          backgroundColor: '#858585',
        },
        '.def-button-red': {
          backgroundColor: 'red',
        },
      }
      addUtilities(newUtilities)
    }
  ],
}
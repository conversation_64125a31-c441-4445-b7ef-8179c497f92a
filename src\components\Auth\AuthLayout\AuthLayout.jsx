import { Link, Outlet } from "react-router-dom"
import inkredologo from "@/assets/inkredo.svg"

import ScrollToTop from "@/components/shared/Routing/ScrollToTop.jsx";

function AuthLayout() {
    return (
        <>
            <ScrollToTop />
            <main className="min-h-screen overflow-hidden flex items-center justify-center">
                <div className="max-w-[600px] w-[90%] h-[700px] bg-main rounded-[30px] flex items-center justify-center">
                    <div className="flex-col h-full w-[90%] flex items-center justify-center">
                        <Link to={"/"}><img src={inkredologo} alt="logo" className="block h-[5vh]" /></Link>
                        <Outlet />
                    </div>
                </div>
            </main>
        </>
    )
}

export default AuthLayout
import { contactColorsTailwind, contractStatusTypes } from "@/util/constants";
import { searchType } from "@/util/searchHelpers";
import { useNavigate } from "react-router-dom";
import { Tooltip } from "@mui/material";
import { tooltipProps } from "@/util/tooltipProps";

function TableData({ contract }) {
    const navigate = useNavigate();

    function moveToDetail(id) {
        navigate(`${id}`);
    }

    return (
        <tr onClick={() => moveToDetail(contract.id)} className={`${contactColorsTailwind[contract.color] || "bg-white"} cursor-pointer`}>
            <td>{contract.unique_id}</td>
            <Tooltip title={contract.customer?.fullname} arrow placement="bottom" {...tooltipProps}>
                <td className="max-w-[7vw] truncate">{contract.customer?.fullname}</td>
            </Tooltip>
            <td>{contract.contract_date}</td>
            <Tooltip title={contract.product_name} arrow placement="bottom" {...tooltipProps}>
                <td className="max-w-[5vw] truncate">{contract.product_name}</td>
            </Tooltip>
            <Tooltip title={contract.guarantor?.fullname} arrow placement="bottom" {...tooltipProps}>
                <td className="max-w-[7vw] truncate">{contract.guarantor?.fullname}</td>
            </Tooltip>
            <td>{contract.remaining_debt}</td>
            <Tooltip title={contract.executor?.fullname} arrow placement="bottom" {...tooltipProps}>
                <td className="max-w-[7vw] truncate">{contract.executor?.fullname}</td>
            </Tooltip>
            <td>{searchType(contractStatusTypes, contract.status).name}</td>
        </tr>
    )
}

export default TableData

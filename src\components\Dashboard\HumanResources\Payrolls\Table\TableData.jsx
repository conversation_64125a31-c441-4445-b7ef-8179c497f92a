function TableData({ payroll, handleSelectedIds, selectedIds }) {
    const color = payroll?.status == "PAID" ? "green" : "red";
    const text = payroll?.status == "PAID" ? "Ödəndi" : "Ödənməyib";

    return (
        <tr className={`text-[${color}]`}>
            <td>
                <input
                    className="scale-125"
                    type="checkbox"
                    name={`payroll-checkbox-${payroll?.id}`}
                    checked={selectedIds.includes(payroll?.id)}
                    onChange={() => handleSelectedIds("update", payroll?.id)}
                />
            </td>

            <td>{payroll?.fullname}</td>
            <td>{payroll?.position}</td>
            <td>{payroll?.salary}</td>
            <td>{text}</td>
        </tr>
    );
}

export default TableData;

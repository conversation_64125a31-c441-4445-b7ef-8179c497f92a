import { useState } from "react";
import { Line<PERSON>hart, Line, XAxis, <PERSON><PERSON><PERSON>s, CartesianGrid, <PERSON>lt<PERSON>, Responsive<PERSON><PERSON><PERSON>, <PERSON> } from "recharts";
import DatePicker from "react-datepicker";

import { useGetAllInvestmentsQuery } from "@/redux/services/statsApiSlice";
import {
  findRangeDiffType,
  normalizeDate,
  prepareOneWeekInterval,
} from "@/util/dateHelpers";
import { formatYAxisValue, prepareChartRequestParams, transformDataForChart } from "@/util/formatters";

const InvestmentsChart = () => {
  const oneWeekInterval = prepareOneWeekInterval();
  const [dateRange, setDateRange] = useState(oneWeekInterval);
  const [fromDate, toDate] = dateRange || [];

  const rangeType = fromDate && toDate ? findRangeDiffType(fromDate, toDate) : null;

  const params = prepareChartRequestParams(fromDate, toDate);

  const {
    data: allRaw,
  } = useGetAllInvestmentsQuery(params, {
    skip: !fromDate || !toDate,
  });

  const transformInvestmentsData = (backendData, rangeType, dateRange) => {
    if (!backendData) return [];

    const investments = backendData.INVESTMENT || {};
    const withdraws = backendData.WITHDRAW || {};

    const investmentSeries = transformDataForChart(investments, rangeType, dateRange);
    const withdrawSeries = transformDataForChart(withdraws, rangeType, dateRange);

    const maxLength = Math.max(investmentSeries.length, withdrawSeries.length);

    const mergedData = Array.from({ length: maxLength }, (_, i) => ({
      name: investmentSeries[i]?.name || withdrawSeries[i]?.name || "",
      əlavələr: investmentSeries[i]?.value || 0,
      çəkimlər: withdrawSeries[i]?.value || 0,
    }));


    return mergedData;
  };

  const chartData = transformInvestmentsData(allRaw, rangeType, { fromDate, toDate });

  return (
    <div className="p-4 rounded-lg shadow bg-white">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-semibold">İnvestisiyalar</h2>
        <div className="w-64">
          <DatePicker
            selectsRange
            startDate={dateRange[0]}
            endDate={dateRange[1]}
            onChange={update => {
              if (Array.isArray(update)) {
                setDateRange(
                  update.map(d => (d ? normalizeDate(d) : null))
                );
              } else {
                setDateRange(update);
              }
            }}
            isClearable
            showWeekNumbers
            className="w-full text-[14px] placeholder:text-[#666666] outline-none"
          />
        </div>
      </div>
      <div className="h-[180px]">
        <ResponsiveContainer width="100%" height="100%">
          <LineChart data={chartData} margin={{ top: 5, right: 20, bottom: 5, left: 0 }}>
            <CartesianGrid strokeDasharray="3 3" horizontal={true} vertical={false} />
            <XAxis dataKey="name" axisLine={false} tickLine={false} />
            <YAxis axisLine={false} tickLine={false} tickFormatter={formatYAxisValue} />
            <Tooltip formatter={(value, name) => [`₼${value.toLocaleString()}`, name]} />
            <Legend />
            <Line
              type="monotone"
              dataKey="əlavələr"
              stroke="#4CAF50"
              strokeWidth={2}
              dot={false}
              activeDot={false}
            />
            <Line
              type="monotone"
              dataKey="çəkimlər"
              stroke="#F44336"
              strokeWidth={2}
              dot={false}
              activeDot={false}
            />
          </LineChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

export default InvestmentsChart;

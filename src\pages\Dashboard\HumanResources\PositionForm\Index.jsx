import { useForm } from 'react-hook-form';
import { TextField, MenuItem, Select, FormControl } from '@mui/material';
import { Link, useNavigate, useParams } from 'react-router-dom';
import ContentLoadingScreen from '@/components/shared/LoadingScreen/ContentLoadingScreen';
import {
  useAddPositionMutation,
  useGetSinglePositionQuery,
  useUpdatePositionMutation,
} from '@/redux/services/userApiSlice';
import { useEffect } from 'react';
import { showToast } from '@/util/showToast';
import { errorFinder } from '@/util/errorHandler';
import { yupResolver } from '@hookform/resolvers/yup';
import { positionFormSchema } from '@/util/schemas/positionFormSchema';

function PositionForm() {
  const { id } = useParams();
  const { data: position, isLoading: getSinglePositionLoading } = useGetSinglePositionQuery(id, { skip: !id });
  const [addPosition, { isLoading: addPositionLoading }] = useAddPositionMutation();
  const [updatePosition, { isLoading: updatePositionLoading }] = useUpdatePositionMutation();

  const navigate = useNavigate();

  const {
    register,
    handleSubmit,
    reset,
    clearErrors,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(positionFormSchema),
    defaultValues: {
      name: position?.name || '',
      starting_salary: position?.starting_salary || '',
      final_salary: position?.final_salary || '',
      work_day: position?.work_day || '',
      status: position?.status || 'Ödənmədi',
    },
    mode: "onBlur",
    reValidateMode: "onSubmit",
  });


  useEffect(() => {
    if (position) {
      reset({
        name: position?.name || '',
        starting_salary: position?.starting_salary || '',
        final_salary: position?.final_salary || '',
        work_day: position?.work_day || '',
        status: position?.status || 'Ödənmədi',
      });
    }
  }, [position, reset]);

  const onSubmit = async (data) => {
    try {
      if (id) {
        await updatePosition({ id, data }).unwrap();
        showToast("Vəzifə uğurla yeniləndi", "success");
      } else {
        await addPosition(data).unwrap();
        navigate("/hr/positions");
      }
    } catch (err) {
      showToast(errorFinder(err), "error");
    }
  };

  if (getSinglePositionLoading) {
    return (
      <div className="w-full h-[calc(100vh-200px)] flex justify-center items-center">
        <ContentLoadingScreen />
      </div>
    );
  }

  return (
    <div className="mt-5 h-full w-full pr-7">
      <form className="max-w-[600px] w-full" onSubmit={handleSubmit(onSubmit)}>
        <div className="flex items-center gap-2 mb-8">
          <span className="min-w-[200px]">Vəzifə</span>
          <TextField
            {...register('name')}
            onChange={() => clearErrors('name')}
            fullWidth
            type="text"
            variant="standard"
            placeholder="Vəzifə adı"
            size="small"
            error={!!errors.name}
            helperText={errors.name?.message}
          />
        </div>

        <div className="flex items-center gap-2 mb-8">
          <span className="min-w-[200px]">Başlanğıc əmək haqqı</span>
          <TextField
            {...register('starting_salary')}
            fullWidth
            type="number"
            variant="standard"
            placeholder="Əmək haqqı"
            size="small"
          />
        </div>

        <div className="flex items-center gap-2 mb-8">
          <span className="min-w-[200px]">Yekun əməkhaqqı</span>
          <TextField
            {...register('final_salary')}
            fullWidth
            type="number"
            variant="standard"
            placeholder="Yekun əməkhaqqı"
            size="small"
          />
        </div>

        <div className="flex items-center gap-2 mb-8">
          <span className="min-w-[200px]">İş günü</span>
          <TextField
            {...register('work_day')}
            fullWidth
            type="text"
            variant="standard"
            placeholder="İş günü"
            size="small"
          />
        </div>

        <div className="flex items-center gap-2 mb-8">
          <span className="min-w-[200px]">Status</span>
          <FormControl variant="standard" fullWidth>
            <Select {...register('status')} defaultValue="Ödənmədi">
              <MenuItem value="Ödəndi">Ödəndi</MenuItem>
              <MenuItem value="Ödənmədi">Ödənmədi</MenuItem>
            </Select>
          </FormControl>
        </div>

        <div className="fixed bottom-8 right-10 flex items-center gap-[12px]">
          <button
            type="submit"
            className="def-button def-button-success"
            disabled={addPositionLoading || updatePositionLoading}
          >
            Yadda saxla
          </button>
          <Link to={".."} className="def-button def-button-gray">
            Geri
          </Link>
        </div>
      </form>
    </div>
  );
}

export default PositionForm;

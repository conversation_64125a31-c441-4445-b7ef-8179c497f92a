import { useState, useRef, useEffect, useCallback } from "react";
import { Helmet } from "react-helmet-async";
import ContentLoadingScreen from "@/components/shared/LoadingScreen/ContentLoadingScreen";
import { useForm, FormProvider } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { administratorFormSchema } from "@/util/schemas/administratorFormSchema";
import { useAddUserMutation, useGetSingleUsersQuery, useUpdateUserMutation } from "@/redux/services/userApiSlice";
import { showToast } from "@/util/showToast";
import { errorFinder } from "@/util/errorHandler";
import { getToday } from "@/util/dateHelpers";
import { useParams } from "react-router-dom";
import { objectFormatter, urlToFile } from "@/util/formatters";
import { useDispatch, useSelector } from "react-redux";
import { clearSelectedValues } from "@/redux/features/infiniteScrollSlice";
import { UserTypeSection } from "@/components/Dashboard/HumanResources/AdministratorForm/UserTypeSection";
import { PhoneSection } from "@/components/Dashboard/HumanResources/AdministratorForm/PhoneSection";
import { CitySalaryEmailSection } from "@/components/Dashboard/HumanResources/AdministratorForm/CitySalaryEmailSection";
import { ProfileImageSection } from "@/components/Dashboard/HumanResources/AdministratorForm/ProfileImageSection";
import { SaveButton } from "@/components/Dashboard/HumanResources/AdministratorForm/SaveButton";

export default function AdministratorForm() {
  const { id } = useParams();
  const dispatch = useDispatch();
  const { data: admin, isLoading: loadingAdmin } = useGetSingleUsersQuery(id, { skip: !id });

  const methods = useForm({
    resolver: yupResolver(administratorFormSchema),
    mode: "onBlur",
    reValidateMode: "onSubmit"
  });
  const { handleSubmit, setValue, clearErrors } = methods;

  const selectedUser = useSelector(state => state.infiniteScroll.administrator.selectedValue);

  const [userType, setUserType] = useState("new");
  const [formKey, setFormKey] = useState(0);
  const [imagePreview, setImagePreview] = useState(null);
  const fileInputRef = useRef(null);

  const clearImage = useCallback(() => {
    setImagePreview(null);
    setValue("profileImage", "");
    if (fileInputRef.current) fileInputRef.current.value = "";
  }, [setValue]);

  useEffect(() => {
    if (admin?.profile_image && id) {
      const url = admin.profile_image;
      setImagePreview(url);
      urlToFile(url, setValue);
    }
    if (admin && id) {
      setUserType("new");
      setValue("fullname", admin.fullname || "");
    }
  }, [admin, id, setValue]);

  useEffect(() => {
    if (userType === "existing" && !id) {
      if (selectedUser) {
        Object.entries({ fullname: selectedUser.fullname, phone1: selectedUser.phone1, phone2: selectedUser.phone2, email: selectedUser.email, salary: selectedUser.salary, city: selectedUser.city?.id }).forEach(([key, val]) => setValue(key, val || ""));
        if (selectedUser.profile_image) setImagePreview(selectedUser.profile_image);
        clearErrors();
        setFormKey(prev => prev + 1);
      } else {
        ["fullname", "phone1", "phone2", "email", "salary", "city", "profileImage"].forEach(field => setValue(field, ""));
        setImagePreview(null);
        clearErrors();
        setFormKey(prev => prev + 1);
      }
    }
  }, [selectedUser, userType, id, setValue, clearErrors, clearImage]);

  useEffect(() => () => dispatch(clearSelectedValues(["administrator"])), [dispatch]);

  const [addUser, { isLoading: adding }] = useAddUserMutation();
  const [updateUser, { isLoading: updating }] = useUpdateUserMutation();

  const onSubmit = async (data) => {
    try {
      if (!id && userType === "existing" && selectedUser) {
        const payload = objectFormatter({ ...data, is_empoyee: true });
        if (payload.salary == null) delete payload.salary;
        await updateUser({ data: payload, id: selectedUser.id }).unwrap();
        showToast("Mövcud istifadəçi administrator kimi təyin edildi", "success");
        return;
      }
      const base = { ...data, date_joined: getToday(), phone2: data.phone2 || null, receipt: null, city: data.city, is_empoyee: true };
      const payload = objectFormatter(base);
      if (payload.salary == null) delete payload.salary;
      if (!payload.phone2) delete payload.phone2;

      if (id) {
        await updateUser({ data: payload, id }).unwrap();
        showToast("İstifadəçi uğurla yeniləndi", "success");
      } else {
        await addUser(payload).unwrap();
        showToast("İstifadəçi uğurla əlavə edildi", "success");
      }
    } catch (err) {
      showToast(errorFinder(err), "error");
    }
  };

  const handleImageChange = (e) => {
    const file = e.target.files?.[0];
    clearErrors("profileImage");
    if (file) {
      setImagePreview(URL.createObjectURL(file));
      setValue("profileImage", file);
    } else clearImage();
  };

  return (
    <>
      <Helmet><title>inKredo | {id ? "Administrator Düzəliş" : "Administrator Əlavə et"}</title></Helmet>
      {loadingAdmin ? (
        <div className="w-full h-[calc(100vh-200px)] flex justify-center items-center">
          <ContentLoadingScreen />
        </div>
      ) : (
        <FormProvider {...methods}>
          <form onSubmit={handleSubmit(onSubmit)} className="max-w-1/3 w-full pt-5">
            <div className="space-y-4">
              <UserTypeSection
                id={id}
                userType={userType}
                setUserType={setUserType}
                formKey={formKey}
                setFormKey={setFormKey}
                selectedUser={selectedUser}
                clearImage={clearImage}
              />
              <PhoneSection formKey={formKey} admin={admin} />
              <CitySalaryEmailSection formKey={formKey} admin={admin} />
              <ProfileImageSection
                formKey={formKey}
                imagePreview={imagePreview}
                fileInputRef={fileInputRef}
                handleImageChange={handleImageChange}
                clearImage={clearImage}
              />
            </div>
            <SaveButton onClick={handleSubmit(onSubmit)} disabled={adding || updating} />
          </form>
        </FormProvider>
      )}
    </>
  );
}

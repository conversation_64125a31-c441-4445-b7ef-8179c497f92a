import ContentLoadingScreen from "@/components/shared/LoadingScreen/ContentLoadingScreen"
import PaginationComponent from "@/components/shared/Pagination/PaginationComponent";
import { useGetAllPositionsQuery } from "@/redux/services/userApiSlice";

import generalTableStyles from "@/styles/shared/generalTableStyles.module.css";
import { useSelector } from "react-redux";
import { Link, useSearchParams } from "react-router-dom";
import TableData from "@/components/Dashboard/HumanResources/Positions/Table/TableData";
import TableHeader from "@/components/Dashboard/HumanResources/Positions/Table/TableHead";

function PositionsPage() {
  const [searchParams] = useSearchParams();
  // const dispatch = useDispatch();

  const limit = searchParams.get("limit") || "15";
  const offset = searchParams.get("offset") || "0";

  const queryObject = {
    limit,
    offset,
    ...Object.fromEntries(searchParams.entries()),
  };

  const { data: positions, isLoading, isError, error } = useGetAllPositionsQuery(queryObject);

  const { isSearchOpen } = useSelector(state => state.layout);

  if (isError) return (
    <p className="errorText">Bir xəta baş verdi xaiş edirik bir daha cəhd edin. Error: {error?.originalStatus || error?.status}</p>
  )

  return (
    <div className="pb-8 pt-[24px] w-full">
      <div className="mb-[24px] gap-[28px] flex flex-col items-start md:flex-row md:items-center">
        <Link to="new" className="def-button def-button-success">
          Vəzifə əlavə et
        </Link>
      </div>

      <div
        className={`${generalTableStyles.tableWrapper} ${isSearchOpen && generalTableStyles.searchOpened} h-[calc(100vh-280px)]`}
      >
        {isLoading ? (
          <ContentLoadingScreen />
        ) : (
          positions
            && positions.results
            && positions.results.length > 0
            ? <table className={`${generalTableStyles.table} !table-fixed`}>
              <TableHeader />
              <tbody className={generalTableStyles.tbody}>
                {positions &&
                  positions.results.map((position) => (
                    <TableData key={position.id} position={position} />
                  ))}
              </tbody>
              <tfoot className={generalTableStyles.tfoot}>
                <tr>
                  <td>Sıralanır: {positions.count}</td>
                  <td colSpan={6}></td>
                </tr>
              </tfoot>
            </table>
            : <p className="text-center my-[40px]">Məlumat tapılmadı</p>
        )}
      </div>
      {positions
        && !isLoading
        && (
          <PaginationComponent
            totalCount={positions.count}
          />
        )}
    </div>
  )
}

export default PositionsPage
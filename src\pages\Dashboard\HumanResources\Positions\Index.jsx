import ContentLoadingScreen from "@/components/shared/LoadingScreen/ContentLoadingScreen"
import PaginationComponent from "@/components/shared/Pagination/PaginationComponent";
import { useGetAllPositionsQuery } from "@/redux/services/userApiSlice";


import { useSelector } from "react-redux";
import { Link, useSearchParams } from "react-router-dom";
import TableData from "@/components/Dashboard/HumanResources/Positions/Table/TableData";
import TableHeader from "@/components/Dashboard/HumanResources/Positions/Table/TableHead";

function PositionsPage() {
  const [searchParams] = useSearchParams();
  // const dispatch = useDispatch();

  const limit = searchParams.get("limit") || "15";
  const offset = searchParams.get("offset") || "0";

  const queryObject = {
    limit,
    offset,
    ...Object.fromEntries(searchParams.entries()),
  };

  const { data: positions, isLoading, isError, error } = useGetAllPositionsQuery(queryObject);

  const { isSearchOpen } = useSelector(state => state.layout);

  if (isError) return (
    <p className="errorText">Bir xəta baş verdi xaiş edirik bir daha cəhd edin. Error: {error?.originalStatus || error?.status}</p>
  )

  return (
    <div className="pb-8 pt-[24px] w-full">
      <div className="mb-[24px] gap-[28px] flex flex-col items-start md:flex-row md:items-center">
        <Link to="new" className="def-button def-button-success">
          Vəzifə əlavə et
        </Link>
      </div>

      <div
        className={`w-[calc(100%-70px)] sm:w-[calc(100%-90px)] overflow-auto rounded-[20px] h-[calc(100vh-280px)] transition-all duration-300 ease-in-out ${isSearchOpen ? 'w-[calc(100%-80px)] sm:w-[calc(100%-320px)]' : ''}`}
      >
        {isLoading ? (
          <ContentLoadingScreen />
        ) : (
          positions
            && positions.results
            && positions.results.length > 0
            ? <table className="w-full border-2 border-table-border border-separate border-spacing-0 rounded-[20px] table-fixed">
              <TableHeader />
              <tbody className="[&_td]:py-[10px] [&_td]:px-2 [&_td]:border-b-2 [&_td]:border-table-border [&_td]:text-sm [&_td]:whitespace-nowrap [&_tr:last-child_td]:border-b-0 [&_tr_td:first-child]:pl-[13px] [&_tr_td:last-child]:pr-[13px] [&_tr]:border-t-2 [&_tr]:border-b-2 [&_tr]:border-table-border">
                {positions &&
                  positions.results.map((position) => (
                    <TableData key={position.id} position={position} />
                  ))}
              </tbody>
              <tfoot className="sticky bottom-0 h-10 bg-secondary [&_td]:py-[10px] [&_td]:px-2 [&_td]:leading-5 [&_td]:text-sm [&_td]:whitespace-nowrap [&_td]:text-white [&_td:first-child]:rounded-bl-[18px] [&_td:first-child]:pl-[13px] [&_td:last-child]:rounded-br-[18px]">
                <tr>
                  <td>Sıralanır: {positions.count}</td>
                  <td colSpan={6}></td>
                </tr>
              </tfoot>
            </table>
            : <p className="text-center my-[40px]">Məlumat tapılmadı</p>
        )}
      </div>
      {positions
        && !isLoading
        && (
          <PaginationComponent
            totalCount={positions.count}
          />
        )}
    </div>
  )
}

export default PositionsPage
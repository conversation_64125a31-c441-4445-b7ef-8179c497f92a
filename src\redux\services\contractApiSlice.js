import { createApi } from "@reduxjs/toolkit/query/react";
import { authBaseQueryWithReauth } from "./baseQueryWithReauth";

export const contractApiSlice = createApi({
  baseQuery: authBaseQueryWithReauth,
  reducerPath: "contractApiSlice",
  tagTypes: ["Contracts", "SingleContract", "Installments"],
  endpoints: (builder) => ({
    addContract: builder.mutation({
      query: (credentials) => ({
        url: `/contracts/create/`,
        method: "POST",
        body: credentials,
      }),
      invalidatesTags: ["Contracts"],
    }),
    getAllContracts: builder.query({
      query: (params) => ({
        url: "/contracts/",
        method: "GET",
        params,
      }),
      providesTags: ["Contracts"],
      keepUnusedDataFor: 1,
    }),
    getSingleContract: builder.query({
      query: (id) => ({
        url: `/contracts/${id}/`,
        method: "GET",
      }),
      providesTags: ["SingleContract"],
      keepUnusedDataFor: 1,
    }),
    updateContractStatus: builder.mutation({
      query: (data) => ({
        url: `/contracts/${data.id}/`,
        method: "PUT",
        body: data.updateStatusData,
      }),
      invalidatesTags: ["SingleContract"],
    }),
    updateContractInfo: builder.mutation({
      query: (data) => ({
        url: `/contracts/${data.id}/`,
        method: "PUT",
        body: data.updateStatusData,
      }),
      invalidatesTags: ["SingleContract"],
    }),
    getContractInstallment: builder.query({
      query: (params) => ({
        url: `/contracts/installments/`,
        method: "GET",
        params: params,
      }),
      providesTags: ["Installments"],
      keepUnusedDataFor: 1,
    }),
    updateInstallment: builder.mutation({
      query: (credentials) => {
        const formData = new FormData();

        for (const key in credentials.data) {
          formData.append(key, credentials.data[key]);
        }

        return {
          url: `/contracts/installments/${credentials.id}/`,
          method: "PUT",
          body: formData,
        };
      },
      invalidatesTags: ["Installments"],
    }),

    payInstallation: builder.mutation({
      query: (data) => {
        const formData = new FormData();

        formData.append("amount", data.amount);
        formData.append("note", data.note || "");
        formData.append("installment", data.installment);
        formData.append("paid_date", data.paid_date);

        if (data.receipt) {
          formData.append("receipt", data.receipt);
        }

        return {
          url: "/contracts/pay-installment/",
          method: "POST",
          body: formData,
        };
      },
      invalidatesTags: ["Installments"],
    }),

    cancelContract: builder.mutation({
      query: (id) => ({
        url: `/contracts/${id}/`,
        method: "DELETE",
      }),
      invalidatesTags: ["SingleContract"],
    }),
    exportContract: builder.mutation({
      query: (body) => ({
        url: "/contracts/export-contracts-to-excel/",
        method: "POST",
        body,
        responseHandler: (resp) => resp.blob(),
      }),

      transformResponse: (blob, meta, arg) => {
        let fileName = `contract-${arg.contract_id || "export"}.xlsx`;
        const cd = meta?.response?.headers.get("Content-Disposition");
        if (cd?.includes("filename=")) {
          const m = cd.match(/filename="?([^"]+)"?/);
          if (m?.[1]) fileName = m[1];
        }

        const url = URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = fileName;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        return { success: true };
      },

      keepUnusedDataFor: 0,
    }),
    exportReceipt: builder.mutation({
      query: (body) => ({
        url: "/contracts/create-receipt-pdf/",
        method: "POST",
        body,
        responseHandler: (resp) => resp.blob(),
      }),

      transformResponse: (blob, _, arg) => {
        let fileName = `receipt-${arg.id || "export"}.pdf`;
        // const cd = meta?.response?.headers.get("Content-Disposition");
        // if (cd?.includes("filename=")) {
        //   const m = cd.match(/filename="?([^"]+)"?/);
        //   if (m?.[1]) fileName = m[1];
        // }

        const url = URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = fileName;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        return { success: true };
      },

      keepUnusedDataFor: 0,
    }),

    getAllInstallments: builder.query({
      query: (params) => ({
        url: "/contracts/installments/",
        method: "GET",
        params,
      }),
      keepUnusedDataFor: 1,
      providesTags: ["Installments"],
    }),
  }),
});

export const {
  useAddContractMutation,
  useGetAllContractsQuery,
  useGetSingleContractQuery,
  useUpdateContractStatusMutation,
  useUpdateContractInfoMutation,
  useGetContractInstallmentQuery,
  usePayInstallationMutation,
  useCancelContractMutation,
  useExportContractMutation,
  useExportReceiptMutation,
  useUpdateInstallmentMutation,
  useGetAllInstallmentsQuery,
} = contractApiSlice;

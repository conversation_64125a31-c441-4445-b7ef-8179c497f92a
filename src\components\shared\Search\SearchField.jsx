import { TextField, FormControl, InputLabel, MenuItem, Select } from "@mui/material";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import PhoneInput from "../Phone/PhoneInput";
import CitySelector from "../CitySelector/CitySelector";

function SearchField({
  type = 'text',
  name,
  label,
  value,
  onChange,
  options = [],
  inputProps = {},
  fieldProps = {}
}) {
  const handlePhoneChange = (val) => {
    onChange({ target: { name, value: val } });
  };

  switch (type) {
    case 'text':
      return (
        <div {...fieldProps}>
          <TextField
            id={`search-${name}`}
            name={name}
            label={label}
            type="search"
            variant="standard"
            size="small"
            className="w-full"
            value={value || ""}
            onChange={onChange}
            {...inputProps}
          />
        </div>
      );

    case 'phone':
      return (
        <div {...fieldProps}>
          <PhoneInput
            value={value}
            onChange={handlePhoneChange}
            name={name}
            label={label}
            id={`search-${name}`}
          />
        </div>
      );

    case 'select':
      return (
        <div {...fieldProps}>
          <FormControl variant="standard" sx={{ m: 1, minWidth: 120, width: "100%" }}>
            <InputLabel>{label}</InputLabel>
            <Select
              value={value || ""}
              onChange={onChange}
              name={name}
              {...inputProps}
            >
              {options.map((option, index) => (
                <MenuItem key={index} value={option.value}>
                  {option.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </div>
      );

    case 'date':
      return (
        <div className={`mt-[20px] mb-8 ${fieldProps.className || ''}`}>
          <DatePicker
            className="w-full placeholder:text-[#666666] outline-none text-[14px]"
            selectsRange={true}
            startDate={value?.[0] || null}
            endDate={value?.[1] || null}
            onChange={(update) => onChange({ target: { name, value: update } })}
            isClearable={true}
            placeholderText={label || "Tarix"}
            {...inputProps}
          />
        </div>
      );

    case 'city':
      return (
        <div {...fieldProps}>
          <CitySelector
            isSearch={true}
            customOnChange={(newValue) => {
              onChange({ target: { name: "customer__city", value: newValue ? newValue.id : null } });
            }}
          />
        </div>
      );

    default:
      return null;
  }
}

export default SearchField;

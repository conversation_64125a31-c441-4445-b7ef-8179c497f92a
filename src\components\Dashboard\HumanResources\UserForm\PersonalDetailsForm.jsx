import { Checkbox, FormControl, FormControlLabel, FormHelperText, MenuItem, Select, TextField } from "@mui/material";
import CitySelector from "@/components/shared/CitySelector/CitySelector";
import { useParams } from "react-router-dom";
import { useFormContext } from "react-hook-form";
import PhoneInput from "@/components/shared/Phone/PhoneInput";

function PersonalDetailsForm({ user = {}, userFormConfig, userProfile }) {
    const {
        register,
        formState: { errors },
        control,
        clearErrors
    } = useFormContext();

    const { id } = useParams();

    const canAddOrEditEmail = userFormConfig?.[userProfile.role].canAddOrEditEmail;
    const canAddUserAsEmployee = userFormConfig?.[userProfile.role].canAddUserAsEmployee;

    const cityValue = {
        city: user?.city?.id
    };

    return (
        <div className="max-w-[426px] w-full">
            <TextField
                id="standard-required"
                defaultValue={id ? user.fullname : ""}
                label="Ad Soyad Ata adı"
                variant="standard"
                {...register("fullname")}
                error={!!errors?.fullname}
                helperText={errors?.fullname?.message}
                className="w-full"
                required
            />

            <div className="flex justify-between my-[27px] items-center">
                <label className="text-[14px] font-[500]" htmlFor="requiredPhone">
                    Tel nömrəsi *
                </label>
                <div className="flex flex-col w-1/2">
                    <PhoneInput
                        control={control}
                        name="phone1"
                        defaultValue={user?.phone1 || ""}
                        errors={errors}
                        onChange={() => clearErrors("phone1")}
                    />
                </div>
            </div>

            <div className="flex justify-between my-[27px] items-center">
                <label className="text-[14px] font-[500]" htmlFor="optionalPhone">
                    Tel nömrəsi
                </label>
                <div className="flex flex-col w-1/2">
                    <PhoneInput
                        control={control}
                        name="phone2"
                        errors={errors}
                        defaultValue={user?.phone2 || ""}
                        onChange={() => clearErrors("phone2")}
                    />
                </div>
            </div>

            {canAddOrEditEmail
                && <div className="flex justify-between my-[27px] items-center">
                    <label className="text-[14px] font-[500]" htmlFor="email">
                        Email
                    </label>
                    <div className="flex flex-col w-1/2">
                        <TextField
                            {...register("email")}
                            type="email"
                            onChange={() => clearErrors("email")}
                            placeholder="<EMAIL>"
                            defaultValue={user?.email || ""}
                            variant="standard"
                            error={!!errors?.email}
                            helperText={errors?.email?.message}
                            size="small"
                            className="w-full"
                        />
                    </div>
                </div>
            }

            <div className="flex justify-between items-center my-[27px]">
                <span className="text-[14px] font-[500]">Şəhər *</span>
                <CitySelector user={cityValue} width="w-1/2" errors={errors} control={control} />
            </div>
            {userProfile?.role === "admin" && (
                <div className="flex justify-between items-center">
                    <span className="text-[14px] font-[500]">Rol *</span>
                    <FormControl
                        className="w-1/2"
                        variant="standard"
                        required
                        error={!!errors?.role}
                    >
                        <Select
                            labelId="role-label"
                            id="role"
                            size="small"
                            defaultValue={user?.role || ""}
                            {...register("role")}
                        >
                            <MenuItem value="manager">Menecer</MenuItem>
                            <MenuItem value="admin">Admin</MenuItem>
                        </Select>
                        {errors?.role && (
                            <FormHelperText>{errors.role.message}</FormHelperText>
                        )}
                    </FormControl>
                </div>
            )}

            {
                canAddUserAsEmployee
                && <div className="flex justify-between items-center my-[27px]">
                    <span className="text-[14px] font-[500]">İşçi kimi əlavə et</span>
                    <FormControlLabel
                        className="w-1/2 justify-end"
                        control={
                            <Checkbox
                                {...register("is_employee")}
                                defaultChecked={!!user?.is_employee}
                            />
                        }
                        label=""
                    />
                </div>
            }
        </div>
    );
}

export default PersonalDetailsForm;
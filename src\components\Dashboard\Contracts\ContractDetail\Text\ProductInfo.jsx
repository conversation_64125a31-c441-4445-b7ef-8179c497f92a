import { TextField } from "@mui/material"
import classes from "./contractdetailtext.module.css"
import Tooltip from '@mui/material/Tooltip'

function ProductInfo({ contractDetail, isEdit }) {
    return (
        <div className="flex-grow flex-shrink basis-[200px]">
            <div className={`flex justify-between mb-[10px] items-${isEdit ? "center" : "start"}`}>
                <p className={`w-1/2 ${classes.contractTextLabel}`}>Məhsul adı:</p>
                {isEdit ? (
                    <TextField
                        variant="standard"
                        multiline
                        fullWidth
                        name="product_name"
                        className="w-1/2"
                        defaultValue={contractDetail.product_name || ""}
                    />
                ) : (
                    <Tooltip title={contractDetail.product_name} arrow>
                        <p className={`${classes.contractTextValue}`}>{contractDetail.product_name}</p>
                    </Tooltip>
                )}
            </div>
            <div className="flex justify-between mb-[10px]">
                <p className={`w-1/2 ${classes.contractTextLabel}`}>Miqdarı</p>
                <p className={`${classes.contractTextValue}`}>{contractDetail.product_quantity}</p>
            </div>
            <div className="flex justify-between mb-[10px]">
                <p className={`w-1/2 ${classes.contractTextLabel}`}>İlkin ödəniş (₼)</p>
                <p className={`${classes.contractTextValue}`}>{contractDetail.initial_payment}</p>
            </div>
            <div className="flex justify-between mb-[10px]">
                <p className={`w-1/2 ${classes.contractTextLabel}`}>Aylıq ödəniş (₼)</p>
                <p className={`${classes.contractTextValue}`}>{contractDetail.monthly_payment}</p>
            </div>
            <div className="flex justify-between mb-[10px]">
                <p className={`w-1/2 ${classes.contractTextLabel}`}>Məhsul Qiyməti (₼)</p>
                <p className={`${classes.contractTextValue}`}>{contractDetail.product_price}</p>
            </div>
            <div className="flex justify-between mb-[10px]">
                <p className={`w-1/2 ${classes.contractTextLabel}`}>Komissiya (₼)</p>
                <p className={`${classes.contractTextValue}`}>{contractDetail.commission}</p>
            </div>
            <div className="flex justify-between mb-[10px]">
                <p className={`w-1/2 ${classes.contractTextLabel}`}>Xərc (₼)</p>
                <p className={`${classes.contractTextValue}`}>{contractDetail?.cost_amount}</p>
            </div>
            <div className="flex justify-between mb-[10px]">
                <p className={`w-1/2 ${classes.contractTextLabel}`}>Sərmayə (₼)</p>
                <p className={`${classes.contractTextValue}`}>{contractDetail.total_investment}</p>
            </div>
        </div>
    )
}

export default ProductInfo

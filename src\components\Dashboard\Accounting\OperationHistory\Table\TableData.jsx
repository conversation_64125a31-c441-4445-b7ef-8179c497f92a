
import { transformText } from "@/util/formatters";
import { tooltipProps } from "@/util/tooltipProps";
import { Link } from "react-router-dom";
import { Tooltip } from "@mui/material";

function TableData({ data }) {
  const renderTransformedText = (data, text, option = "default") => {
    const result = transformText(data, text);

    if (result.type === "investor" || result.type === "contract") {
      return (
        <>
          {result.before}
          <Link className={`${option === "tooltip" ? "text-white font-bold underline" : "text-[#094c89] font-[500]"}`} to={result.linkTo}>{result.linkText}</Link>
          {result.after}
        </>
      );
    }

    return result.text;
  };

  const transformedText = renderTransformedText(data, data.operation);
  const tooltipTransformedText = renderTransformedText(data, data.operation, "tooltip");

  return (
    <tr>
      <td>{data.id}</td>
      <td className="whitespace-nowrap">{data.operation_date}</td>
      <Tooltip title={tooltipTransformedText} arrow placement="bottom" {...tooltipProps}>
        <td className="whitespace-nowrap truncate max-w-[300px]">{transformedText}</td>
      </Tooltip>
      <td>
        <Link className="text-[#094c89] font-[500]" to={`/hr/users/${data.executor?.id}`}>
          {data.executor?.fullname}
        </Link>
      </td>
    </tr>
  );
}

export default TableData;

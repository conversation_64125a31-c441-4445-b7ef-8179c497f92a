function UsernameInput({ register = null, errors = {}, disabled = false }) {
  return (
    <p className="my-[18px]">
      <input
        className="w-full py-[11px] px-[18px] outline-0 bg-white rounded-[5px] placeholder:text-modal-placeholder-border"
        placeholder="İstifadəçi adı"
        type="text"
        disabled = {disabled}
        {...(register ? register("username") : {name: "username"})}
      />
      {register && <span className="text-red-600 h-[23px] block text-[13px] font-[600]">{errors.username?.message}</span>}
    </p>
  )
}

export default UsernameInput
function ResetPass() {
  return (
    <div className="w-full flex flex-col">
      <form className="w-full">
        <div className="w-full mt-[42px] mb-[34px]">
          <p className="text-center text-modal-placeholder-border"><EMAIL> hesabına yeniləmə keçidi
            göndəriləcək.</p>
          <p className="my-[18px]"><input className="w-full py-[11px] px-[18px] outline-0 bg-white rounded-[5px] placeholder:text-modal-placeholder-border" placeholder="E-mail daxil edin" type="mail" /></p>
        </div>
        <button className="defButton bgGreen mx-auto block mb-[18px]">Daxil ol</button>
      </form>
    </div>
  )
}

export default ResetPass
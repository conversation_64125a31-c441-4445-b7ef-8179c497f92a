import TableRow from "./TableRow"

function TableBody({ data }) {
    return (
        <tbody className="[&_td]:py-[10px] [&_td]:px-2 [&_td]:border-b-2 [&_td]:border-table-border [&_td]:text-sm [&_td]:whitespace-nowrap [&_tr:last-child_td]:border-b-0 [&_tr_td:first-child]:pl-[13px] [&_tr_td:last-child]:pr-[13px] [&_tr]:border-t-2 [&_tr]:border-b-2 [&_tr]:border-table-border">
            {data.map((item, index) => (
                <TableRow key={index} item={item} />
            ))}
        </tbody>
    )
}

export default TableBody
import { TextField } from "@mui/material"

import { useSelector } from "react-redux"
import UserAutocompleteWrapper from "@/components/shared/Autocomplete/UserAutocompleteWrapper"

function CustomerInfo() {
    const { attorney, guarantor } = useSelector(state => state.infiniteScroll);

    const date = new Date().toISOString().split("T")[0];

    return (
        <div className=" my-div-f">
            <style>{`.my-div-f {
                        width: auto;
                        min-width: 350px;}

                        @media (max-width: 991px) {
                            .my-div {
                            min-width: unset;
                            }
                        }
            `}</style>
            <h2 className="pb-[10px] border-b border-gray-custom font-bold text-lg"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> məlumatları</h2>
            <div>
                <div className={`flex items-center justify-between my-[14px]`}>
                    <span className="w-1/3 text-[14px]"><PERSON><PERSON><PERSON><PERSON>əri *</span>
                    <div className="w-2/3">
                        <UserAutocompleteWrapper type="customer" />
                    </div>
                </div>
                <div className={`flex items-center justify-between my-[14px]`}>
                    <span className="w-1/3 text-[14px]">Kəfil</span>
                    <div className="w-2/3">
                        <UserAutocompleteWrapper type="guarantor" isOptionalField={true} />
                    </div>
                </div>
                <div className="flex items-center justify-between my-[14px]">
                    <span className="w-1/3 text-[14px]">Kəfil tel:</span>
                    <TextField className="w-2/3" variant="standard" disabled value={guarantor.selectedValue?.phone1 || ""} />
                </div>

                <div className={`flex items-center justify-between my-[14px]`}>
                    <span className="w-1/3 text-[14px]">Vəkil</span>
                    <div className="w-2/3">
                        <UserAutocompleteWrapper type="attorney" isOptionalField={true} />
                    </div>
                </div>
                <div className="flex items-center justify-between my-[14px]">
                    <span className="w-1/3 text-[14px]">Vəkil tel:</span>
                    <TextField className="w-2/3" variant="standard" disabled value={attorney.selectedValue?.phone1 || ""} />
                </div>
            </div>
            <div className="flex justify-between">
                <label className="w-1/3 text-[14px]" htmlFor="contract_date">Tarix *</label>
                <input defaultValue={date} className="w-2/3 border-b border-[#707070] outline-none text-[14px]" type="date" name="contract_date" id="contract_date" required />
            </div>
            <div className="mt-10">
                <TextField
                    id="outlined-multiline-static"
                    name="note"
                    label="Qeyd"
                    multiline
                    minRows={4}
                    className="w-full"
                    variant="outlined"
                />
            </div>
        </div>
    )
}

export default CustomerInfo
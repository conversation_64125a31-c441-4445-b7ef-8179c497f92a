import { useDispatch } from "react-redux";
import { handleModal, setModalData } from "@/redux/features/modal/contractPaymentSlice";

import swal from "sweetalert";
import { CircularProgress, Tooltip } from "@mui/material";
import { contactColorsTailwind } from "@/util/constants";
import { useExportReceiptMutation, useUpdateInstallmentMutation } from "@/redux/services/contractApiSlice";
import { showToast } from "@/util/showToast";
import { errorFinder } from "@/util/errorHandler";
import { useState } from "react";
import ImagePreviewModal from "./ImagePreviewModal";

function TableData({ installment, status, installmentArray, columns, contractUIState, setContractUIState }) {
    const dispatch = useDispatch();
    const [exportReceipt, { isLoading: exportReceiptLoading }] = useExportReceiptMutation();
    const [updateInstallment, { isLoading: updateInstallmentLoading }] = useUpdateInstallmentMutation();

    const [open, setOpen] = useState(false);
    const [modalContent, setModalContent] = useState(null);

    function handleRowDoubleClick() {
        const firstToBePaidCredit = installmentArray.find(
            (item) =>
                item.payment_status == "INCOMPLETE" || item.payment_status == "UNPAID"
        )?.id;

        if (status == "CANCELLED") {
            swal({
                title: "Müqavilə artıq ləğv edilib",
                icon: "error",
            });
            return;
        }
        else if (status == "WAITING") {
            swal({
                title: "Gözləmə statusunda olan müqavilədə ödəniş etmək olmaz",
                icon: "error",
            });
            return;
        }

        if (installment.id > firstToBePaidCredit) {
            swal({
                title: `İlk öncə ${firstToBePaidCredit} ID-li krediti ödəyin`,
                icon: "error",
            });
            return;
        }

        if (installment.payment_status == "PAID" || installment.payment_status == "OVERPAYMENT") {
            swal({
                title: "Bu kredit artıq ödənib",
                icon: "error",
            });
            return;
        }

        dispatch(handleModal());
        dispatch(setModalData({
            installment: installment.id,
            amount: installment.payment_status == "INCOMPLETE"
                ? (parseFloat(installment.amount) - parseFloat(installment.paid_amount)).toFixed(2)
                : parseFloat(installment.amount),
        }));
    }

    function handleContextMenu(e) {
        e.preventDefault();
        installment.payment_status != "UNPAID"
            && setContractUIState({
                isContextMenuOpen: true,
                top: e.pageY,
                left: e.pageX,
                contract: {
                    id: installment.id,
                    paid_amount: installment.paid_amount
                }
            });
    }

    onclick = () => {
        contractUIState.isContextMenuOpen && setContractUIState({
            ...contractUIState,
            isContextMenuOpen: false,
            top: 0,
            left: 0,
        });
    };

    async function handleDownloadReceipt() {
        try {
            await exportReceipt({ id: installment.id }).unwrap();
        } catch (err) {
            showToast(errorFinder(err), "error");
        }
    }

    function handleDocumentClick() {
        const receiptUrl = installment.receipt;

        if (!receiptUrl) return;

        const isPdf = receiptUrl.endsWith(".pdf");

        if (isPdf) {
            try {
                const link = document.createElement("a");
                link.href = receiptUrl;
                link.setAttribute("download", "receipt.pdf");
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            } catch {
                showToast("Sənəd yüklənə bilmədi", "error");
            }
        } else {
            setModalContent(receiptUrl);
            setOpen(true);
        }

    }

    function handleClose() {
        setOpen(false);
        setModalContent(null);
    }

    async function handleInputChange(e) {
        const file = e.target.files[0];

        const data = {
            id: installment.id,
            data: {
                receipt: file
            }
        };

        try {
            await updateInstallment(data).unwrap();
        } catch (err) {
            showToast(errorFinder(err), "error");
        }
    }

    return (
        <>
            <ImagePreviewModal handleClose={handleClose} modalContent={modalContent} open={open} />

            <tr
                onContextMenu={handleContextMenu}
                onDoubleClick={handleRowDoubleClick}
                className={contactColorsTailwind[installment.color] || "bg-white"}
            >
                {columns.map((column) => {
                    if (column.hasTooltip) {
                        return (
                            <td key={column.accessor}>
                                <Tooltip title={installment[column.accessor]} arrow placement="bottom">
                                    <div className="overflow-hidden whitespace-nowrap text-ellipsis max-w-[100px]">
                                        {installment[column.accessor]}
                                    </div>
                                </Tooltip>
                            </td>
                        )
                    }
                    if (column.renderCell) {
                        return (
                            <td className="whitespace-nowrap" key={column.accessor}>
                                {column.renderCell(installment)}
                            </td>
                        );
                    }
                    if (column.isReceipt) {
                        return (
                            installment.payment_status === "PAID" || installment.payment_status === "OVERPAYMENT" ? (
                                <td className="whitespace-nowrap" key={column.accessor}>
                                    <span
                                        onClick={handleDownloadReceipt}
                                        className={`underline cursor-pointer inline-flex items-center gap-1`}
                                    >
                                        {exportReceiptLoading ? "Endirilir..." : "Endir"}
                                        {exportReceiptLoading && <CircularProgress size={18} />}
                                    </span>
                                </td>
                            ) : (
                                <td className="whitespace-nowrap" key={column.accessor}></td>
                            )
                        );
                    }
                    if (column.isDocument) {
                        return (
                            <td className="whitespace-nowrap" key={column.accessor}>
                                {installment.receipt ? (
                                    <button
                                        onClick={handleDocumentClick}
                                        className="underline cursor-pointer inline-flex items-center gap-1"
                                    >
                                        Bax
                                        {updateInstallmentLoading && <CircularProgress size={12} />}
                                    </button>
                                ) : (
                                    <label
                                        className={`underline cursor-pointer inline-flex items-center gap-1`}
                                    >
                                        {updateInstallmentLoading ? "Yüklənir..." : "Yüklə"}
                                        {updateInstallmentLoading && <CircularProgress size={12} />}
                                        <input
                                            name="receipt"
                                            onChange={handleInputChange}
                                            className="hidden"
                                            type="file"
                                            accept="image/*,application/pdf"
                                        />
                                    </label>
                                )}
                            </td>
                        );
                    }
                    return (
                        <td className="whitespace-nowrap" key={column.accessor}>
                            {installment.is_active && column.isNumber ? 0 : installment[column.accessor]}
                        </td>
                    );
                })}
            </tr>
        </>
    )
}

export default TableData;

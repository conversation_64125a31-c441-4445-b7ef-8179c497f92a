function EditInstallmentBtn({ contractUIState, openModal }) {
    return (
        <div
            className="absolute bg-white border border-table-border rounded-lg shadow-[0px_4px_8px_rgba(0,0,0,0.1)] z-[1000]"
            style={{
                top: contractUIState.top,
                left: contractUIState.left,
            }}
        >
            <button
                className="px-4 py-2 w-full text-left hover:bg-gray-100 rounded transition-colors"
                onClick={() => openModal()}>
                Düzəliş et
            </button>
        </div>
    )
}

export default EditInstallmentBtn
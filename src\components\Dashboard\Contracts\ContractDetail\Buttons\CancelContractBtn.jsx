import { useParams } from "react-router-dom";

import { useCancelContractMutation } from "@/redux/services/contractApiSlice";
import { errorFinder } from "@/util/errorHandler";
import { showToast } from "@/util/showToast";

function CancelContractBtn({ contractDetail }) {
    const { id } = useParams();

    const [cancelContract, { isLoading: cancelContractLoading }] = useCancelContractMutation();

    async function handleContractDelete() {
        try {
            const response = await cancelContract(id).unwrap();
            const msg = errorFinder(response);

            showToast(msg, "success");
        }
        catch (error) {
            console.log(error);
            const msg = errorFinder(error);

            showToast(msg, "error");
        }
    }

    return (
        <>
            {
                contractDetail.status != "WAITING" &&
                <button
                    disabled={
                        cancelContractLoading ||
                        contractDetail.status == "CANCELLED" ||
                        contractDetail.status == "COMPLETED"
                    }
                    className="def-button def-button-red mb-5"
                    onClick={handleContractDelete}>{cancelContractLoading ? "Ləğv edilir" : "Müqaviləni ləğv et"}
                </button>
            }
        </>
    )
}

export default CancelContractBtn
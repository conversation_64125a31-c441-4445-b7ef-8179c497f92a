function InvestorItem({ investor, modalHandler }) {
    return (
        <tr className="border-t-2 border-b-2 border-table-border">
            <td className="py-[10px] px-2 pl-[13px] border-b-2 border-table-border text-sm whitespace-nowrap overflow-hidden text-ellipsis max-w-[300px]" title={investor.id}>{investor.id}</td>
            <td className="py-[10px] px-2 border-b-2 border-table-border text-sm whitespace-nowrap overflow-hidden text-ellipsis max-w-[300px]" title={investor?.created_date}>{investor?.created_date}</td>
            <td onClick={() => modalHandler("portmanatPreview", investor.user?.id)} className="py-[10px] px-2 border-b-2 border-table-border text-sm whitespace-nowrap overflow-hidden text-ellipsis max-w-[300px] cursor-pointer" title={investor.user?.fullname}>{investor.user?.fullname}</td>
            <td className="py-[10px] px-2 border-b-2 border-table-border text-sm whitespace-nowrap overflow-hidden text-ellipsis max-w-[300px]" title={investor.period_in_months}>{investor.period_in_months}</td>
            <td className="py-[10px] px-2 border-b-2 border-table-border text-sm whitespace-nowrap overflow-hidden text-ellipsis max-w-[300px]" title={investor.previous_balance}>{investor.previous_balance}</td>
            <td className="py-[10px] px-2 border-b-2 border-table-border text-sm whitespace-nowrap overflow-hidden text-ellipsis max-w-[300px]" title={investor.money_pool_share_coefficient}>{investor.money_pool_share_coefficient}</td>
            <td className="py-[10px] px-2 border-b-2 border-table-border text-sm whitespace-nowrap overflow-hidden text-ellipsis max-w-[300px]" title={investor.monthly_profit}>{investor.monthly_profit}</td>
            <td className="py-[10px] px-2 pr-[13px] border-b-2 border-table-border text-sm whitespace-nowrap overflow-hidden text-ellipsis max-w-[300px]" title={investor.balance}>{investor.balance}</td>
        </tr>
    )
}

export default InvestorItem
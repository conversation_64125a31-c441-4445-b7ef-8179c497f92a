import SearchComponent from "@/components/shared/Search/SearchComponent";

function InstallmentSearch() {
    const searchFields = [
        {
            type: 'text',
            name: 'customer',
            label: '<PERSON>üştəri axtar',
        },
        {
            type: 'city',
            name: 'customer__city',
            label: '<PERSON><PERSON><PERSON><PERSON><PERSON> adı seç',
        },
        {
            type: 'text',
            name: 'guarantor',
            label: '<PERSON>əfi<PERSON>',
        },
        {
            type: 'text',
            name: 'product_name',
            label: '<PERSON>üqavilə predmeti',
        },
        {
            type: 'date',
            name: 'date',
            label: 'Tarix',
            fieldProps: { className: 'mt-[20px] mb-8' }
        }
    ];

    const defaultValues = {
        customer: '',
        customer__city: '',
        guarantor: '',
        product_name: '',
        date: [null, null]
    };

    return (
        <div>
            <SearchComponent
                fields={searchFields}
                defaultValues={defaultValues}
                limit="13"
            />
        </div>
    );
}

export default InstallmentSearch
function EditActions({ toggleEditState, formRef, isLoading }) {
    function handleCancelClick() {
        toggleEditState();
    }

    return (
        <>
            <button
                disabled={isLoading}
                className="def-button def-button-gray mb-5"
                onClick={handleCancelClick}
            >
                Ləğv Et
            </button>
            <button
                disabled={isLoading}
                className="def-button def-button-success mb-5"
                onClick={() => formRef.current?.requestSubmit()}
            >

                {isLoading ? "Yaddaşa verilir..." : "Yaddaşa ver"}
            </button>
        </>
    )
}

export default EditActions
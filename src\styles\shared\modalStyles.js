export const createModalRootStyles = ({
  maxWidth = "400px",
  height = "380px",
  topPadding = "36px",
  width = "100%",
  minWidth = "300px",
}) => ({
  slotprops: {
    backdrop: {
      sx: {
        backdropFilter: "blur(5px)",
        backgroundColor: "rgba(255,255,255,0.2)",
      },
    },
  },
  sxstyle: {
    "& .MuiDialog-container": {
      "& .MuiPaper-root": {
        width,
        maxWidth,
        height,
        boxShadow: "0px 4px 4px 0px #00000040",
        borderRadius: "10px",
        padding: `${topPadding} 50px 59px`,
        minWidth
      },
      "& .MuiDialogContent-root": {
        padding: "0 2px",
        width: "100%",
        justifyContent: "center",
        alignItems: "center",
        display: "flex",
      },
    },
  },
});

export const modalStyles = {
  payment: createModalRootStyles({
    maxWidth: "450px",
    height: "550px",
    topPadding: "46px",
  }),
  balance: createModalRootStyles({ maxWidth: "600px", height: "450px" }),
  userForm: createModalRootStyles({ maxWidth: "400px", height: "380px" }),
  investors: createModalRootStyles({ maxWidth: "500px", height: "600px" }),
  portmanatTransfer: createModalRootStyles({
    maxWidth: "500px",
    height: "600px",
  }),
  installmentUpdate: createModalRootStyles({
    maxWidth: "500px",
    height: "300px",
  }),
  portmanatPreview: createModalRootStyles({
    height: "400px",
    maxWidth: "initial",
    width: "fit-content",
    minWidth: "300px"
  }),
};

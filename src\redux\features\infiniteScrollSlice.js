import { createSlice } from "@reduxjs/toolkit";

const dataTemplate = {
  searchQuery: "",
  isListOpen: false,
  selectedValue: null,
  error: false,
};

const initialState = {
  customer: { ...dataTemplate },
  guarantor: { ...dataTemplate },
  attorney: { ...dataTemplate },
  executor: { ...dataTemplate },
  investor: { ...dataTemplate },
  expense: { ...dataTemplate },
  administrator: { ...dataTemplate },
  // New reusable types
  position: { ...dataTemplate },
  city: { ...dataTemplate },
  activeUsers: { ...dataTemplate },
};

const infiniteScroll = createSlice({
  name: "infiniteScroll",
  initialState,
  reducers: {
    setData: (state, action) => {
      const { mainKey, key, value } = action.payload;
      if (state[mainKey]) {
        state[mainKey][key] = value;
      }
    },
    clearData: (state) => {
      Object.assign(state, initialState);
    },
    clearSelectedValues: (state, action) => {
      const keys = action.payload;
      keys.forEach((key) => (state[key].selectedValue = null));
    },
  },
});

export const { setData, clearData, clearSelectedValues } = infiniteScroll.actions;
export default infiniteScroll.reducer;

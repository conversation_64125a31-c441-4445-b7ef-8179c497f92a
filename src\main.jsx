import { createRoot } from 'react-dom/client'
import { create<PERSON>rowser<PERSON>outer, RouterProvider } from 'react-router-dom'
import { Provider } from 'react-redux'
import { HelmetProvider } from 'react-helmet-async'

import "@/styles/index.css"
import "@/styles/App.css"

import { store } from '@/redux/app/store.js'
import ErrorPage from '@/pages/ErrorPage.jsx'
import Login from '@/pages/Auth/Login/Login.jsx'
import Register from '@/pages/Auth/Register/Register.jsx'
import ProtectedRoute from '@/components/shared/Routing/ProtectedRoute.jsx'
import Layout from '@/components/Dashboard/Layout/Layout.jsx'
import Homepage from '@/pages/Dashboard/Review/Index.jsx'
import UserProfile from '@/pages/Dashboard/UserProfile/Index.jsx'
import InvestorsPage from '@/pages/Dashboard/Accounting/Investors/Index.jsx'
import OperationHistoryPage from '@/pages/Dashboard/Accounting/OperationHistory/Index.jsx'
import BalancePage from '@/pages/Dashboard/Accounting/Balance/Index.jsx'
import AuthLayout from '@/components/Auth/AuthLayout/AuthLayout.jsx'
import UsersPage from '@/pages/Dashboard/HumanResources/Users/<USER>'
import UserFormPage from '@/pages/Dashboard/HumanResources/UserForm/Index.jsx'
import AddContractPage from '@/pages/Dashboard/Contracts/AddContract/Index.jsx'
import ContractsPage from '@/pages/Dashboard/Contracts/Contracts/Index.jsx'
import ContractDetailPage from '@/pages/Dashboard/Contracts/ContractDetail/Index.jsx'
import withRoleAccess from '@/components/shared/Routing/withRoleAccess.jsx';
import InstallmentsPage from '@/pages/Dashboard/Contracts/Installments/Index.jsx'
import AdministratorsPage from '@/pages/Dashboard/HumanResources/Administrators/Index.jsx'
import AdministratorForm from '@/pages/Dashboard/HumanResources/AdministratorForm/Index.jsx'
import PositionsPage from './pages/Dashboard/HumanResources/Positions/Index'
import PayrollsPage from './pages/Dashboard/HumanResources/Payrolls/Index'
import PositionForm from './pages/Dashboard/HumanResources/PositionForm/Index'

const ProtectedBalancePage = withRoleAccess(BalancePage, 'balance');
const ProtectedInvestorsPage = withRoleAccess(InvestorsPage, 'investors');
const ProtectedAdministratorsPage = withRoleAccess(AdministratorsPage, 'administrators');
const ProtectedAdministratorFormPage = withRoleAccess(AdministratorForm, 'administrators');
const ProtectedPayrollsPage = withRoleAccess(PayrollsPage, 'payrolls');
const ProtectedPositionsPage = withRoleAccess(PositionsPage, 'positions');
const ProtectedPositionFormPage = withRoleAccess(PositionForm, 'positions');

const router = createBrowserRouter([
  {
    path: "/",
    element: <ProtectedRoute><Layout /></ProtectedRoute>,
    errorElement: <ErrorPage />,
    children: [
      {
        index: true,
        element: <Homepage />,
      },
      {
        path: "/profile",
        element: <UserProfile />
      },
      {
        path: "contracts",
        children: [
          {
            index: true,
            element: <ContractsPage />,
          },
          {
            path: ":id",
            element: <ContractDetailPage />,
          },
          {
            path: "installments",
            element: <InstallmentsPage />,
          },
        ],
      },
      {
        path: "hr",
        children: [
          {
            path: "users",
            children: [
              {
                index: true,
                element: <UsersPage />,
              },
              {
                path: ":id",
                element: <UserFormPage />,
              },
              {
                path: ":customerParamId/contract",
                element: <AddContractPage />,
              },
              {
                path: "new/contract",
                element: <AddContractPage />,
              },
              {
                path: "new",
                element: <UserFormPage />,
              },
            ]
          },
          {
            path: "admin",
            children: [
              {
                index: true,
                element: <ProtectedAdministratorsPage />,
              },
              {
                path: "new",
                element: <ProtectedAdministratorFormPage />,
              },
              {
                path: ":id",
                element: <ProtectedAdministratorFormPage />,
              },
            ]
          },
          {
            path: "payrolls",
            element: <ProtectedPayrollsPage />
          },
          {
            path: "positions",
            children: [
              {
                index: true,
                element: <ProtectedPositionsPage />,
              },
              {
                path: "new",
                element: <ProtectedPositionFormPage />,
              },
              {
                path: ":id",
                element: <ProtectedPositionFormPage />,
              },
            ]
          },
        ]
      },
      {
        path: "accounting",
        children: [
          {
            path: "investors",
            element: <ProtectedInvestorsPage />
          },
          {
            path: "ops",
            element: <OperationHistoryPage />
          },
          {
            path: "balance",
            element: <ProtectedBalancePage />
          },
        ]
      },
    ],
  },
  {
    element: <AuthLayout />,
    children: [
      {
        path: "/login",
        element: <Login />
      },
      {
        path: "/register",
        element: <Register />
      },
    ]
  }
], {
  future: {
    v7_fetcherPersist: true,
    v7_relativeSplatPath: true,
    v7_normalizeFormMethod: true,
    v7_partialHydration: true,
    v7_skipActionErrorRevalidation: true,
  },
});

createRoot(document.getElementById('root')).render(
  <Provider store={store}>
    <HelmetProvider>
      <RouterProvider future={{
        v7_startTransition: true,
      }} router={router} />
    </HelmetProvider>
  </Provider>
)

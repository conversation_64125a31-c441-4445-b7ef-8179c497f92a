import { TextField } from "@mui/material";

export function CustomeTextField({
    name,
    color,
    value,
    ...props
}) {
    return (
        <TextField
            required
            type="number"
            variant="standard"
            name={name}
            value={value}
            {...props}
            sx={{
                "& .MuiInputBase-input": {
                    color: color,
                    fontSize: "14px",
                }
            }}
        />
    );
}
import { Dialog, DialogContent, TextField } from "@mui/material"
import { modalStyles } from "@/styles/shared/modalStyles";
import { useDispatch, useSelector } from "react-redux"
import { handleModal } from "@/redux/features/modal/balanceModalSlice";

import { useUpdateBalanceMutation } from "@/redux/services/accountingApiSlice";
import { objectFormatter } from "@/util/formatters";
import { errorFinder } from "@/util/errorHandler";
import { showToast } from "@/util/showToast";

function ActionModal({ isOpen = false }) {
    const dispatch = useDispatch();
    const { modalData } = useSelector(state => state.balanceModal);

    const [updateBalance, { isLoading: udpateBalanceLoading }] = useUpdateBalanceMutation();

    async function handleSubmit(e) {
        e.preventDefault();

        const formData = Object.fromEntries(new FormData(e.target).entries());
        formData[modalData.name] = Number(formData[modalData.name]);

        try {
            await updateBalance(objectFormatter(formData));
            dispatch(handleModal())
        } catch (error) {
            console.error(error);
            const msg = errorFinder(error);

            showToast(msg, "error");
        }
    }

    return (
        <Dialog
            open={isOpen}
            sx={modalStyles.balance.sxstyle}
            slotProps={modalStyles.balance.slotprops}
            onClose={() => dispatch(handleModal())}
        >
            <DialogContent>
                <form action="" onSubmit={handleSubmit} className="w-full">
                    <h2 className={`modalTitle mb-[30px]`}>Əməliyyat</h2>
                    <div className="flex items-center w-full mb-10">
                        <span className="w-1/3 text-[14px]">{modalData.title}</span>
                        <input
                            className="border-b border-black outline-none w-2/3"
                            step={"any"}
                            name={modalData.name}
                            type="number"
                            defaultValue={modalData.value}
                        />
                    </div>
                    <div className="mt-[32px] mb-[45px]">
                        <TextField className="w-full" label="Qeyd" multiline name="note" rows={4} />
                    </div>
                    <div className="flex gap-[28px] justify-end">
                        <button type="button" onClick={() => dispatch(handleModal())} className="def-button def-button-gray">Ləğv et</button>
                        <button
                            className="def-button def-button-success"
                            disabled={udpateBalanceLoading}
                        >
                            Yenilə
                        </button>
                    </div>
                </form>
            </DialogContent>
        </Dialog>
    )
}

export default ActionModal

import { useState } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, ResponsiveContainer } from "recharts";
import DatePicker from "react-datepicker";
import { useGetAllCostsQuery } from "@/redux/services/statsApiSlice";
import { normalizeDate, prepareOneWeekInterval } from "@/util/dateHelpers";
import { prepareChartRequestParams } from "@/util/formatters";

const CostsChart = () => {
  const oneWeekInterval = prepareOneWeekInterval();
  const [dateRange, setDateRange] = useState(oneWeekInterval);
  const [fromDate, toDate] = dateRange || [];
  const COLORS = ["#007bff", "#e9ecef"];

  const params = prepareChartRequestParams(fromDate, toDate);

  const {data, isLoading, isFetching, isError, error } = useGetAllCostsQuery(params, {
    skip: !fromDate || !toDate,
  });

  const chartData = data ? [{ name: "<PERSON><PERSON><PERSON>l<PERSON><PERSON>", value: data.total_cost }] : [];
  const total = data?.total_cost || 0;

  return (
    <div className="p-4 rounded-lg shadow bg-white">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-semibold">Xərclər</h2>
        <div className="w-64">
          <DatePicker
            selectsRange
            startDate={dateRange[0]}
            endDate={dateRange[1]}
            onChange={update => {
              if (Array.isArray(update)) {
                setDateRange(
                  update.map(d => (d ? normalizeDate(d) : null))
                );
              } else {
                setDateRange(update);
              }
            }}
            isClearable
            showWeekNumbers
            className="w-full text-[14px] placeholder:text-[#666666] outline-none"
          />
        </div>
      </div>
      <div className="flex justify-center items-center h-[180px]">
        <div className="relative w-[180px] h-[180px]">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={chartData}
                dataKey="value"
                outerRadius={80}
                innerRadius={65}
                startAngle={90}
                endAngle={-390}
              >
                <Cell fill={COLORS[0]} />
              </Pie>
            </PieChart>
          </ResponsiveContainer>
          <div className="absolute inset-0 flex flex-col justify-center items-center">
            <div className="text-3xl font-bold">₼{total}</div>
            <div className="text-sm text-gray-500">Ümumi</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CostsChart;

import { useState } from "react";
import { useSearchParams } from "react-router-dom";

import IncomeExpenseModal from "../ActionModal/IncomeExpenseModal.jsx";
import PortmanatTransferModal from "../ActionModal/PortmanatTransferModal.jsx";
import TableData from "./TableData.jsx";
import TableHeader from "./TableHeader.jsx";

import { useSelector, useDispatch } from "react-redux";
import { useGetAllInvestorsQuery } from "@/redux/services/accountingApiSlice.js";
import PaginationComponent from "@/components/shared/Pagination/PaginationComponent.jsx";
import { roundTo2 } from "@/util/formatters.js";
import ContentLoadingScreen from "@/components/shared/LoadingScreen/ContentLoadingScreen";
import { clearData } from "@/redux/features/infiniteScrollSlice";
import PortmanatPreviewModal from "../ActionModal/PortmanatPreview/PortmanatPreviewModal.jsx";

function InvestorTable() {
  const [searchParams] = useSearchParams();
  const dispatch = useDispatch();

  const limit = searchParams.get("limit") || "16";
  const offset = searchParams.get("offset") || "0";

  const queryObject = {
    limit,
    offset,
    ...Object.fromEntries(searchParams.entries()),
  };

  const { data: investors, isLoading, isError, error } = useGetAllInvestorsQuery(queryObject);

  const [modalState, setModalState] = useState({
    isOpen: false,
    type: null,
    userId: null
  });

  function modalHandler(type = null, userId = null) {
    if (!type) {
      dispatch(clearData());
    }

    setModalState({
      isOpen: type ? true : false,
      type: type,
      userId: userId
    });
  }

  const { isSearchOpen } = useSelector(state => state.layout);
  const { userId } = modalState;

  if (isError) return (
    <p className="errorText">Bir xəta baş verdi xaiş edirik bir daha cəhd edin. Error: {error?.originalStatus || error?.status}</p>
  )

  return (
    <div className="pb-8 pt-[24px] w-full">
      <div className="mb-[24px] gap-[28px] flex flex-col items-start md:flex-row md:items-center">
        <button onClick={() => modalHandler("investor")} className="def-button def-button-success">
          İnvestisiya əlavə et
        </button>
        <button onClick={() => modalHandler("expense")} className="def-button def-button-success">Pul çəkimi</button>
        <button onClick={() => modalHandler("transfer")} className="def-button def-button-success">Transfer</button>
      </div>

      {modalState.isOpen && (modalState.type === "investor" || modalState.type === "expense") && (
        <IncomeExpenseModal
          isOpen={modalState.isOpen}
          modalHandler={modalHandler}
          modalType={modalState.type}
          investors={investors?.results || []}
        />
      )}

      {modalState.isOpen && modalState.type === "transfer" && (
        <PortmanatTransferModal
          isOpen={modalState.isOpen}
          modalHandler={modalHandler}
          investors={investors?.results || []}
        />
      )}

      {modalState.isOpen && modalState.type === "portmanatPreview" && (
        <PortmanatPreviewModal
          isOpen={modalState.isOpen}
          modalHandler={modalHandler}
          userId={userId}
        />
      )}

      <div
        className={`w-[calc(100%-70px)] sm:w-[calc(100%-90px)] overflow-auto rounded-[20px] h-[calc(100vh-220px)] transition-all duration-300 ease-in-out ${isSearchOpen ? 'w-[calc(100%-80px)] sm:w-[calc(100%-320px)]' : ''}`}
      >
        {isLoading ? (
          <ContentLoadingScreen />
        ) : (
          investors
            && investors.results
            && investors.results.data
            && investors.results.data.length > 0
            ? <table className="w-full border-2 border-table-border border-separate border-spacing-0 rounded-[20px]">
              <TableHeader />
              <tbody className="[&_td]:py-[10px] [&_td]:px-2 [&_td]:border-b-2 [&_td]:border-table-border [&_td]:text-sm [&_td]:whitespace-nowrap [&_tr:last-child_td]:border-b-0 [&_tr_td:first-child]:pl-[13px] [&_tr_td:last-child]:pr-[13px] [&_tr]:border-t-2 [&_tr]:border-b-2 [&_tr]:border-table-border">
                {investors &&
                  investors.results.data.map((investor) => (
                    <TableData key={investor.id} investor={investor} modalHandler={modalHandler} />
                  ))}
              </tbody>
              <tfoot className={generalTableStyles.tfoot}>
                <tr>
                  <td>Sıralanır: {investors.count}</td>
                  <td colSpan={3}></td>
                  <td className="text-white">{roundTo2(investors.results.extra.total_previous_balance)}</td>
                  <td colSpan={2}></td>
                  <td className="text-white">{roundTo2(investors.results.extra.total_final_balance)}</td>
                </tr>
              </tfoot>
            </table>
            : <p className="text-center my-[40px]">Məlumat tapılmadı</p>
        )}
      </div>
      {investors
        && !isLoading
        && (
          <PaginationComponent
            totalCount={investors.count}
          />
        )}
    </div>
  );
}

export default InvestorTable;

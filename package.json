{"name": "inkredo", "private": true, "version": "1.1.11", "type": "module", "scripts": {"dev": "vite --host", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "optimize-svg": "npx svgo -f src/assets -o src/assets/optimized", "deploy": "deploy.sh"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@hookform/resolvers": "^5.0.1", "@mui/icons-material": "^7.0.2", "@mui/material": "^7.0.2", "@reduxjs/toolkit": "^2.7.0", "react": "^19.1.0", "react-datepicker": "^8.3.0", "react-dom": "^19.1.0", "react-helmet-async": "^2.0.5", "react-hook-form": "^7.56.1", "react-phone-number-input": "^3.4.12", "react-redux": "^9.2.0", "react-router-dom": "^7.5.2", "react-toastify": "^11.0.5", "recharts": "^2.15.3", "sweetalert": "^2.1.2", "yup": "^1.6.1"}, "devDependencies": {"@babel/core": "^7.27.1", "@eslint/js": "^9.25.1", "@tailwindcss/postcss": "^4.1.4", "@tailwindcss/vite": "^4.1.4", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "babel-plugin-transform-remove-console": "^6.9.4", "eslint": "^9.25.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.0.0", "postcss": "^8.5.3", "tailwindcss": "^4.1.4", "vite": "^6.3.3", "vite-plugin-pwa": "^1.0.0"}}
import SearchComponent from "@/components/shared/Search/SearchComponent";
import { payrollStatusTypes } from "@/util/constants";

function PayrollsSearch() {
  const searchFields = [
    {
      type: 'text',
      name: 'fullname',
      label: 'Ad Soyad',
    },
    {
      type: 'text',
      name: 'position',
      label: 'Vəzifə',
    },
    {
      type: 'select',
      name: 'status',
      label: 'Ödəmə Statusu',
      options: payrollStatusTypes,
    },
    {
      type: 'date',
      name: 'date',
      label: 'Tarix',
      fieldProps: { className: 'mt-[20px] mb-8' }
    }
  ];

  const defaultValues = {
    fullname: '',
    position: '',
    status: '',
    status_display: { name: "<PERSON><PERSON><PERSON><PERSON>", value: "" },
    date_range: [null, null]
  };

  return (
    <SearchComponent
      fields={searchFields}
      defaultValues={defaultValues}
      limit="15"
    />
  )
}

export default PayrollsSearch
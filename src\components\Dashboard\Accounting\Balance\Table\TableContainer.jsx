
import { TableHeader } from "./TableHeader"

function TableContainer({ children, label, sum }) {
    return (
        <div>
            <h2 className="mb-[19px] font-bold text-2xl">{label}</h2>
            <table className="w-full border-2 border-table-border border-separate border-spacing-0 rounded-[20px]">
                <TableHeader />
                {children}
                <tfoot className="sticky bottom-0 h-10 bg-secondary [&_td]:py-[10px] [&_td]:px-2 [&_td]:leading-5 [&_td]:text-sm [&_td]:whitespace-nowrap [&_td]:text-white [&_td:first-child]:rounded-bl-[18px] [&_td:first-child]:pl-[13px] [&_td:last-child]:rounded-br-[18px]">
                    <tr>
                        <td colSpan={2}></td>
                        <td>{sum}</td>
                    </tr>
                </tfoot>
            </table>
        </div>
    )
}

export default TableContainer
/*
  /hr - all (includes /hr/users and /hr/admin)
  /contracts - all,
  /investors - ["admin"]
  /ops - all
  /balance - ["admin"]
*/

export const headerNavConfig = [
  {
    path: "/hr",
    routes: [
      {
        name: "<PERSON>st<PERSON><PERSON><PERSON><PERSON>ilə<PERSON>",
        to: "/hr/users",
        limit: 16,
        rolesCanLook: true,
      },
      {
        name: "<PERSON>dar<PERSON><PERSON>ilər",
        to: "/hr/admin",
        limit: 16,
        rolesCanLook: ["admin"],
      },
      {
        name: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
        to: "/hr/payrolls",
        limit: 17,
        rolesCanLook: ["admin"],
      },
      {
        name: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
        to: "/hr/positions",
        limit: 15,
        rolesCanLook: ["admin"],
      },
    ],
  },
  {
    path: "/contracts",
    routes: [
      {
        name: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
        to: "/contracts",
        limit: 17,
        rolesCanLook: true,
      },
      {
        name: "<PERSON><PERSON><PERSON><PERSON><PERSON>əmə",
        to: "/contracts/installments",
        limit: 17,
        rolesCanLook: true,
      },
    ],
  },
  {
    path: "/accounting",
    routes: [
      {
        name: "İnvestorlar",
        to: "/accounting/investors",
        limit: 15,
        rolesCanLook: ["admin"],
      },
      {
        name: "Əməliyyat Tarixçəsi",
        to: "/accounting/ops",
        limit: 17,
        rolesCanLook: true,
      },
      {
        name: "Balans",
        to: "/accounting/balance",
        limit: null,
        rolesCanLook: ["admin"],
      },
    ],
  },
];
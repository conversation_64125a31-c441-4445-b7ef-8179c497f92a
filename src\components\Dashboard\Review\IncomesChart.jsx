import { useState } from "react";
import { <PERSON><PERSON>hart, Bar, <PERSON>Axis, <PERSON>A<PERSON>s, CartesianGrid, Tooltip, ResponsiveContainer, Cell, Legend } from "recharts";
import DatePicker from "react-datepicker";

import { useGetAllIncomeQuery } from "@/redux/services/statsApiSlice";
import {
  findRangeDiffType,
  normalizeDate,
  prepareOneWeekInterval,
} from "@/util/dateHelpers";
import { formatYAxisValue, prepareChartData, prepareChartRequestParams, preparePreviousDateRange } from "@/util/formatters";

const IncomesChart = () => {
  const oneWeekInterval = prepareOneWeekInterval();
  const [dateRange, setDateRange] = useState(oneWeekInterval);
  const [fromDate, toDate] = dateRange || [];

  const rangeType = fromDate && toDate ? findRangeDiffType(fromDate, toDate) : null;

  const { previousFromDate, previousToDate } = preparePreviousDateRange(rangeType, fromDate, toDate);

  const params = prepareChartRequestParams(previousFromDate, toDate);

  const {
    data: allRaw,
    isLoading,
    isFetching,
    isError,
    error,
  } = useGetAllIncomeQuery(params, {
    skip: !fromDate || !toDate,
  });

  const { mergedChartData, roundedTotal, yAxisMax } = prepareChartData(
    allRaw,
    {
      previousFromDate,
      previousToDate
    },
    {
      fromDate,
      toDate
    },
    rangeType,
  )

  return (
    <div className="p-4 rounded-lg shadow bg-white">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-semibold">Gəlirlər</h2>
        <div className="w-64">
          <DatePicker
            selectsRange
            startDate={dateRange[0]}
            endDate={dateRange[1]}
            onChange={update => {
              if (Array.isArray(update)) {
                setDateRange(
                  update.map(d => (d ? normalizeDate(d) : null))
                );
              } else {
                setDateRange(update);
              }
            }}
            isClearable
            showWeekNumbers
            className="w-full text-[14px] placeholder:text-[#666666] outline-none"
          />
        </div>
      </div>

      <div className="h-[180px]">
        <ResponsiveContainer width="100%" height="100%">
          <BarChart data={mergedChartData} margin={{ top: 5, right: 20, bottom: 5, left: 0 }}>
            <CartesianGrid strokeDasharray="3 3" horizontal vertical={false} />
            <XAxis dataKey="name" axisLine={false} tickLine={false} />
            <YAxis
              axisLine={false}
              tickLine={false}
              domain={[0, yAxisMax]}
              tickFormatter={formatYAxisValue}
            />
            <Tooltip
              content={({ payload }) => {
                if (!payload || payload.length === 0) return null;
                const prev = payload.find(p => p.dataKey === "previousValue");
                const curr = payload.find(p => p.dataKey === "value");
                return (
                  <div className="bg-white p-2 border border-gray-300">
                    {prev && (
                      <div className="text-gray-500 m-0">
                        {prev.payload.previousName} – ₼{prev.value.toLocaleString()}
                      </div>
                    )}
                    {curr && (
                      <div className="text-gray-800 m-0">
                        {curr.payload.name} – ₼{curr.value.toLocaleString()}
                      </div>
                    )}
                  </div>
                );
              }}
            />
            <Legend align="right" verticalAlign="top" />
            <Bar
              dataKey="previousValue"
              name="Öncəki dövr"
              fill="#e0e0e0"
              barSize={20}
            />
            <Bar
              dataKey="value"
              name="Cari dövr"
              fill="#212529"
              barSize={20}
            />
          </BarChart>
        </ResponsiveContainer>
      </div>

      <div className="mt-4 text-center">
        <div className="text-2xl font-bold">₼{roundedTotal}</div>
        <div className="text-sm text-gray-500">Ümumi gəlir</div>
      </div>
    </div>
  );
};

export default IncomesChart;

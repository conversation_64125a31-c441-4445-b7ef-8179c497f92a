function RepeatPassInput({ register, errors = {}, disabled = false }) {
    return (
        <p className="my-[18px]">
            <input
                className="w-full py-[11px] px-[18px] outline-0 bg-white rounded-[5px] placeholder:text-modal-placeholder-border"
                placeholder="<PERSON><PERSON><PERSON>əni təkrarla"
                type="password"
                disabled = {disabled}
                {...(register ? register("password2") : {})}
            />
            {register && <span className="text-red-600 h-[23px] block text-[13px] font-[600]">{errors.password2?.message}</span>}
        </p>
    )
}

export default RepeatPassInput
import * as yup from "yup";
import { emailRegex } from "./profileSchema";

export const administratorFormSchema = yup.object().shape({
  fullname: yup.string().required("Ad Soyad Ata adı daxil edin"),
  phone1: yup.string().required("Nömrə daxil edin"),

  phone2: yup.string(),

  city: yup.string().required("Şəhər daxil edin"),

  email: yup
    .string()
    .test("valid-email", "E-mail düzgün formatda deyil", (value) => {
      return emailRegex.test(value);
    }),

  profileImage: yup.mixed().test("file-required", "Şəkil seçin", (value) => {
    return value instanceof File || (!!value && typeof value === "object");
  }),
});

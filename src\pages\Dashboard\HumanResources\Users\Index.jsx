import { Link, useSearchParams } from "react-router-dom";
import { Helmet } from "react-helmet-async"
import { useSelector } from "react-redux";
import { useGetAllUsersQuery } from "@/redux/services/userApiSlice";
import PaginationComponent from "@/components/shared/Pagination/PaginationComponent";
import ContentLoadingScreen from "@/components/shared/LoadingScreen/ContentLoadingScreen";
import UserGridItem from "@/components/Dashboard/HumanResources/Users/<USER>/UserGridItem";

function UsersPage() {
  const [searchParams] = useSearchParams();

  const limit = parseInt(searchParams.get("limit") || "16", 10);
  const offset = parseInt(searchParams.get("offset") || "0", 10);

  const { isSearchOpen } = useSelector(state => state.layout);

  const queryObject = {
    ...Object.fromEntries(searchParams.entries()),
    limit,
    offset,
  };

  const { data: users, isLoading, isError, error } = useGetAllUsersQuery(queryObject);

  if (isError) return <p className="errorText">Bir xəta baş verdi xaiş edirik bir daha cəhd edin. Error: {error?.originalStatus || error?.status}</p>

  return (
    <>
      <Helmet>
        <title>
          inKredo | İstifadəçilər
        </title>
      </Helmet>
      <div className={`pr-6 py-6 w-[calc(100%-70px)] transition-all duration-300 ease-in-out ${isSearchOpen ? 'w-[calc(100%-290px)]' : ''}`}>
        <Link to="new" className="def-button def-button-secondary">
          İstifadəçi əlavə et
        </Link>
        {isLoading ? (
          <div className="w-full h-[calc(100vh-200px)] flex justify-center items-center">
            <ContentLoadingScreen />
          </div>
        ) : (
          users && users.results.length > 0
            ? <div className="w-full grid grid-cols-[repeat(auto-fit,minmax(230px,1fr))] 2xl:grid-cols-4 gap-6 mt-6">
              {users.results.map((user, index) => (
                <UserGridItem user={user} key={user.id || index} />
              ))}
            </div>
            : <p className="text-center my-[40px]">Məlumat tapılmadı</p>
        )}
        {users
          && !isLoading
          && (
            <PaginationComponent
              totalCount={users.count}
            />
          )}
      </div>
    </>
  )
}

export default UsersPage
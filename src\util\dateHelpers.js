export function getToday() {
  const azerbaijanDate = new Date().toLocaleString("en-GB", {
    timeZone: "Asia/Baku",
  });
  const [day, month, year] = azerbaijanDate.split(",")[0].split("/");
  return `${day}-${month}-${year}`;
}

export function extractDMY(date) {
  if (!(date instanceof Date) || isNaN(date.getTime())) {
    return null;
  }

  const day = String(date.getDate()).padStart(2, "0");
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const year = date.getFullYear();

  return `${day}-${month}-${year}`;
}

export function formatToDMY(dateString) {
  return dateString.split("-").reverse().join("-");
}

export function convertToDate(dateString) {
  if (typeof dateString !== "string") return null;

  const parts = dateString.split("-");
  if (parts.length !== 3) return null;

  const [day, month, year] = parts.map(Number);

  if (isNaN(day) || isNaN(month) || isNaN(year)) return null;

  const date = new Date(year, month - 1, day);
  if (
    date.getFullYear() !== year ||
    date.getMonth() !== month - 1 ||
    date.getDate() !== day
  ) {
    return null;
  }

  return date;
}

export function formatDateForAPI(dateString) {
  const [year, month, day] = dateString.split("-");
  return `${day}-${month}-${year}`;
}

export function getDayOfYear(date) {
  const startOfYear = new Date(date.getFullYear(), 0, 1);
  const diffInMs = date - startOfYear;
  const dayOfYear = Math.floor(diffInMs / (1000 * 60 * 60 * 24)) + 1;
  return dayOfYear;
}

export function findRangeDiffType(fromDate, toDate) {
  try {
    const msPerDay = 1000 * 60 * 60 * 24;
    const diffInMs = Math.abs(toDate.getTime() - fromDate.getTime());
    const diffDays = Math.floor(diffInMs / msPerDay);

    if (diffDays >= 31) {
      return "YEAR";
    } else if (diffDays < 31 && diffDays > 7) {
      return "MONTH";
    } else {
      return "WEEK";
    }
  } catch {
    return "YEAR";
  }
}

export const dateToString = (date) => date.toISOString().split("T")[0];

export const normalizeDate = (date) => {
  const dmy = extractDMY(date);
  return dmy ? convertToDate(dmy) : null;
};

export function prepareOneWeekInterval() {
  const today = normalizeDate(new Date());
  const oneWeekAgo = new Date(today);
  oneWeekAgo.setDate(today.getDate() - 6);

  return [oneWeekAgo, today];
}

export function getTodayDateTimeLocalString() {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, "0");
  const day = String(now.getDate()).padStart(2, "0");
  const hours = String(now.getHours()).padStart(2, "0");
  const minutes = String(now.getMinutes()).padStart(2, "0");

  return `${year}-${month}-${day}T${hours}:${minutes}`;
}

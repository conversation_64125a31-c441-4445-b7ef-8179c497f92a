import { Link, useNavigate } from "react-router-dom";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { useRegisterMutation } from "@/redux/services/publicApiSlice";
import swal from "sweetalert";

import { registerSchema as schema } from "@/util/schemas/authSchemas";

import UsernameInput from "@/components/Auth/Inputs/UsernameInput";
import EmailInput from "@/components/Auth/Inputs/EmailInput";
import PasswordInput from "@/components/Auth/Inputs/PasswordInput";
import RepeatPassInput from "@/components/Auth/Inputs/RepeatPassInput";
import FullnameInput from "@/components/Auth/Inputs/FullnameInput";
import { Helmet } from "react-helmet-async";
import { errorFinder } from "@/util/errorHandler";

function Register() {
  const navigate = useNavigate();
  const [registerUser, { isLoading, error }] = useRegisterMutation();

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
    mode: "onSubmit",
  });

  async function onSubmit(data) {
    const updatedData = {
      ...data,
      username: data.username.toLowerCase(),
      email: data.email.toLowerCase(),
    }

    try {
      await registerUser(updatedData).unwrap();

      swal({
        title: "Qeydiyyat uğurla bitdi, xaiş edirik hesabınıza daxil olun",
        icon: "success"
      }).then(() => {
        navigate("/login");
      });

    } catch (err) {
      console.log(error);
      console.error("Registration error:", err);
    }
  };

  const getErrorMessage = () => {
    if (!error) return "";

    return errorFinder(error) || "Bir xəta baş verdi, xahiş edirik biraz sonra bir daha cəhd edin.";
  };

  return (
    <>
      <Helmet>
        <title>inKredo | Qeydiyyatdan Keç</title>
      </Helmet>
      <div className="w-full flex flex-col">
        <form className="w-full" onSubmit={handleSubmit(onSubmit)}>
            {error &&
              <p className="text-center text-red-500 mt-[25px]">
                {getErrorMessage()}
              </p>
            }
          <div className="w-full mx-auto max-w-[300px] mt-[25px] mb-[34px]">
            <FullnameInput register={register} errors={errors} disabled={isLoading} />
            <UsernameInput register={register} errors={errors} disabled={isLoading} />
            <EmailInput register={register} errors={errors} disabled={isLoading} />
            <PasswordInput register={register} errors={errors} disabled={isLoading} />
            <RepeatPassInput register={register} errors={errors} disabled={isLoading} />
          </div>

          <div className="flex justify-center">
            <button
              type="submit"
              className="def-button def-button-success mx-auto block mb-[18px]"
              disabled={isLoading}
            >
              {isLoading ? "Yüklənir..." : "Qeydiyyatdan keç"}
            </button>
          </div>
        </form>

        <Link className="self-center text-[#007AFF]" to={"/login"}>
          Hesabınız mövcuddur?
        </Link>
      </div>
    </>
  );
}

export default Register;

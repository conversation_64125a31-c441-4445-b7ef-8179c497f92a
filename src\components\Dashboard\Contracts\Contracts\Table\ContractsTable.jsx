import PaginationComponent from "@/components/shared/Pagination/PaginationComponent";
import TableHead from "./TableHead";
import TableData from "./TableData";
import { useSearchParams } from "react-router-dom";
import { useSelector } from "react-redux";
import { useGetAllContractsQuery } from "@/redux/services/contractApiSlice";
import { roundTo2 } from "@/util/formatters";
import ContentLoadingScreen from "@/components/shared/LoadingScreen/ContentLoadingScreen";

function ContractsTable() {
    const [searchParams] = useSearchParams();

    const limit = parseInt(searchParams.get("limit") || "16", 10);
    const offset = parseInt(searchParams.get("offset") || "0", 10);

    const queryObject = {
        ...Object.fromEntries(searchParams.entries()),
        limit,
        offset,
    };

    const { data: contracts, isLoading, isError, error } = useGetAllContractsQuery(queryObject);

    const { isSearchOpen } = useSelector(state => state.layout);

    if (isError) return <p className="text-red-500 mt-10 ml-[10px]">Bir xəta baş verdi xaiş edirik bir daha cəhd edin. Error: {error?.originalStatus || error?.status}</p>

    return (
        <div className="pb-8 pt-[24px] w-full">
            <div className={`w-[calc(100%-70px)] sm:w-[calc(100%-90px)] overflow-auto rounded-[20px] h-[calc(100vh-220px)] transition-all duration-300 ease-in-out ${isSearchOpen ? 'w-[calc(100%-80px)] sm:w-[calc(100%-320px)]' : ''}`}>
                {isLoading ? (
                    <ContentLoadingScreen />
                ) : (
                    contracts
                        && contracts.results
                        && contracts.results.data
                        && contracts.results.data.length > 0 ?
                        <table className="w-full border-2 border-table-border border-separate border-spacing-0 rounded-[20px]">
                            <TableHead />
                            <tbody className="[&_td]:py-[10px] [&_td]:px-2 [&_td]:border-b-2 [&_td]:border-table-border [&_td]:text-sm [&_td]:whitespace-nowrap [&_tr:last-child_td]:border-b-0 [&_tr_td:first-child]:pl-[13px] [&_tr_td:last-child]:pr-[13px] [&_tr]:border-t-2 [&_tr]:border-b-2 [&_tr]:border-table-border">
                                {contracts.results.data.map(contract => <TableData key={contract.id} contract={contract} />)}
                            </tbody>
                            <tfoot className="sticky bottom-0 h-10 bg-secondary [&_td]:py-[10px] [&_td]:px-2 [&_td]:leading-5 [&_td]:text-sm [&_td]:whitespace-nowrap [&_td]:text-white [&_td:first-child]:rounded-bl-[18px] [&_td:first-child]:pl-[13px] [&_td:last-child]:rounded-br-[18px]">
                                <tr>
                                    <td>Sıralanır: {contracts.count}</td>
                                    <td colSpan={4}></td>
                                    <td className="whitespace-nowrap text-white">{roundTo2(contracts.results.total_remaining_debt)}</td>
                                    <td colSpan={2}></td>
                                </tr>
                            </tfoot>
                        </table>
                        : <p className="text-center my-[40px]">Məlumat tapılmadı</p>
                )}
            </div>
            {contracts
                && !isLoading
                && <PaginationComponent totalCount={contracts.count} />}
        </div>
    )
}

export default ContractsTable

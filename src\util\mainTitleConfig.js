export const mainTitleConfig = [
  {
    path: "^/$",
    title: "<PERSON> Səhifə",
  },
  {
    path: "^/profile$",
    title: "İstif<PERSON><PERSON><PERSON><PERSON> məlumatları",
  },
  {
    path: "^/hr/users$",
    title: "İstifadəçilər",
  },
  {
    path: "^/hr/users/new$",
    title: "İstifadəçilər / Əlavə et",
  },
  {
    path: "^/hr/users/\\d+$",
    title: "İstifadəçilər",
    isDynamic: true,
    hasExternalData: true,
  },
  {
    path: "^/hr/users/\\d+/contract$",
    title: "İstifadəçilər / Yeni <PERSON>",
    isDynamic: true,
    hasExternalData: false,
  },
  {
    path: "^/hr/users/new/contract$",
    title: "İstifadəçilər / Yeni Mü<PERSON>vilə",
  },
  {
    path: "^/hr/admin$",
    title: "İstifadəçilər / İdarəçilər",
  },
  {
    path: "^/hr/admin/new$",
    title: "İstifadəçilər / İdarəçilər / Əlavə et",
  },
  {
    path: "^/hr/admin/\\d+$",
    title: "İstifadəçilər / İdarəçilər",
    isDynamic: true,
    hasExternalData: true,
  },
  {
    path: "^/contracts$",
    title: "Müqavilələr",
  },
  {
    path: "^/contracts/\\d+$",
    title: "Müqavilələr",
    isDynamic: true,
    hasExternalData: true,
    key: "contractDetail",
  },
  {
    path: "^/contracts/installments$",
    title: "Müqavilələr / Ödəmə İzləmə",
  },
  {
    path: "^/accounting/investors$",
    title: "Muhasibat / İnvestorlar",
  },
  {
    path: "^/accounting/ops$",
    title: "Muhasibat / Əməliyyat Tarixçəsi",
  },
  {
    path: "^/accounting/balance$",
    title: "Muhasibat / Balans",
  },
];

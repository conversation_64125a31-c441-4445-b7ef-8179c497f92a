import { Link } from "react-router-dom";
import { useDeletePositionMutation } from "@/redux/services/userApiSlice";
import swal from "sweetalert";
import { showToast } from "@/util/showToast";
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';

function TableData({ position }) {
    const [deletePosition, { isLoading: deletePositionLoading }] = useDeletePositionMutation();

    function handleDelete() {
        swal({
            title: "Silməyə əminsiniz?",
            text: "Bu əməliyyatı geri qaytarmaq mümkün deyil",
            icon: "warning",
            buttons: ["Xeyr", "Bəli"],
        })
            .then(async (willDelete) => {
                if (willDelete) {
                    await deletePosition({ id: position.id, data: {} }).unwrap();
                    showToast("Vəzifə uğurla silindi", "success");
                }
            });
    }

    return (
        <tr>
            <td>{position?.id}</td>
            <td>{position?.name}</td>
            <td>{position?.starting_salary}</td>
            <td>{position?.final_salary}</td>
            <td>{position?.work_day}</td>
            <td>{position?.status}</td>
            <td className="flex gap-2">
                <Link to={`${position?.id}`} className="text-blue-500 hover:text-blue-700">
                    <EditIcon fontSize="small" />
                </Link>
                <button onClick={handleDelete} disabled={deletePositionLoading} className="text-red-500 hover:text-red-700">
                    <DeleteIcon fontSize="small" />
                </button>
            </td>
        </tr>
    );
}

export default TableData

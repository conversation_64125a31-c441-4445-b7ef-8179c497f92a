import { Link } from 'react-router-dom'

function UserGridItem({user}) {
    return (
        <Link to={`${user.id}`} className="p-5 bg-secondary rounded-[10px] min-h-[134px] shadow-[-1px_1px_2px_0px_rgba(0,0,0,0.5)] text-white xl:min-w-0">
            <h4 className="font-normal text-sm leading-[21.78px]">{user.fullname}</h4>
            <p className="font-normal text-sm leading-[16.34px] mt-[5px]">{user.phone1}</p>
        </Link>
    )
}

export default UserGridItem
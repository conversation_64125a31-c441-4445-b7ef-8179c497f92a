@import "tailwindcss";

@theme {
  --color-main: #073763;
  --color-secondary: #336491;
  --color-third: #92a1ca;
  --color-success: #21d25c;
  --color-clearall: #efbf3a;
  --color-fullpayment: #ccebcc;
  --color-incompletepayment: #fae9b0;
  --color-latepayment: #F1B6B6;
  --color-gray-custom: #bdbdbd;
  --color-modal-input-border: #303030;
  --color-modal-placeholder-border: #858585;
  --color-cell-selected: #C4C6CC;
  --color-red-custom: #CC1212;
  --color-table-border: rgba(209, 209, 209, 0.31);
  --color-all-paid-contracts: #EEB0FA;
  --color-all-paid-installments: #808080;
  --color-over-payment: #BDCDF8;
}

/* Custom utility classes for buttons */
.def-button {
  color: white;
  font-size: 14px;
  line-height: 19.07px;
  box-shadow: -1px 1px 2px 0px #00000080;
  padding: 9px 30px;
  border-radius: 32px;
  display: inline-block;
  cursor: pointer;
}

.def-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.def-button-main {
  background-color: #073763;
}

.def-button-secondary {
  background-color: #336491;
}

.def-button-success {
  background-color: #21d25c;
}

.def-button-yellow {
  background-color: #efbf3a;
}

.def-button-gray {
  background-color: #858585;
}

.def-button-red {
  background-color: red;
}

import { NavLink, useLocation } from "react-router-dom"
import layoutheaderclasses from './layoutHeader.module.css'
import { useEffect, useState } from "react";
import { headerNavConfig } from "@/util/headerNavConfig";
import { useGetUserProfileQuery } from "@/redux/services/userApiSlice";

function HeaderNav() {
    const [links, setLinks] = useState([]);

    const routeLocation = useLocation();
    const { data: user } = useGetUserProfileQuery();

    useEffect(() => {
        const foundLinksObj = headerNavConfig.find(item => routeLocation.pathname.includes(item.path)) || [];
        console.log(routeLocation.pathname);
        setLinks(foundLinksObj);
    }, [routeLocation.pathname, user?.role]);

    // Function to check if a route should be active
    const isRouteActive = (routePath) => {
        const currentPath = routeLocation.pathname;
        const currentPathWithoutQuery = currentPath.split('?')[0];

        // Get all routes in the current group
        const allRoutesInGroup = links?.routes || [];

        // Find the most specific route that matches the current path
        const matchingRoutes = allRoutesInGroup
            .filter(route => currentPathWithoutQuery.startsWith(route.to))
            .sort((a, b) => b.to.length - a.to.length); // Sort by length descending (most specific first)

        // The route should be active only if it's the most specific match
        const mostSpecificRoute = matchingRoutes[0];
        const shouldBeActive = mostSpecificRoute && mostSpecificRoute.to === routePath;

        return shouldBeActive;
    };

    return (
        <nav className={`flex items-center gap-[24px] px-7 ${layoutheaderclasses.headerBottom}`}>
            {links?.routes?.map((route, index) => (
                route.rolesCanLook == true ||
                    (Array.isArray(route.rolesCanLook) && route.rolesCanLook.includes(user?.role))
                    ? (
                        <NavLink
                            className={() => {
                                const isActive = isRouteActive(route.to);
                                return `!text-[14px] ${isActive ? "!text-[white]" : ""} nav-link-transition`;
                            }}
                            key={index}
                            to={route.to + (route.limit ? `?limit=${route.limit}&offset=0` : "")}
                        >
                            {route.name}
                        </NavLink>
                    ) : null
            ))}
        </nav>
    )
}

export default HeaderNav

import { useSearchParams } from "react-router-dom";


import TableData from "./TableData.jsx";
import TableHeader from "./TableHeader.jsx";
import { useSelector } from "react-redux";
import { useGetAllOpsQuery } from "@/redux/services/accountingApiSlice.js";
import PaginationComponent from "@/components/shared/Pagination/PaginationComponent.jsx";
import ContentLoadingScreen from "@/components/shared/LoadingScreen/ContentLoadingScreen";

function OperationTable() {
  const [searchParams] = useSearchParams();

  const limit = searchParams.get("limit") || "16";
  const offset = searchParams.get("offset") || "0";

  const queryObject = {
    limit,
    offset,
    ...Object.fromEntries(searchParams.entries()),
  };

  const { data: ops, isLoading,  isError, error } = useGetAllOpsQuery(queryObject);

  const { isSearchOpen } = useSelector(state => state.layout);

  if (isError) return (
    <p className="errorText">Bir xəta baş verdi xaiş edirik bir daha cəhd edin. Error: {error?.originalStatus || error?.status}</p>
  )

  return (
    <div className="pb-8 pt-[24px] w-full">
      <div className={`w-[calc(100%-70px)] sm:w-[calc(100%-90px)] overflow-auto rounded-[20px] h-[calc(100vh-220px)] transition-all duration-300 ease-in-out ${isSearchOpen ? 'w-[calc(100%-80px)] sm:w-[calc(100%-320px)]' : ''}`}>
        {isLoading ? (
          <ContentLoadingScreen />
        ) : (
          ops
            && ops.results
            && ops.results.data
            && ops.results.data.length > 0
            ? <table className="w-full border-2 border-table-border border-separate border-spacing-0 rounded-[20px]">
              <TableHeader />
              <tbody className="[&_td]:py-[10px] [&_td]:px-2 [&_td]:border-b-2 [&_td]:border-table-border [&_td]:text-sm [&_td]:whitespace-nowrap [&_tr:last-child_td]:border-b-0 [&_tr_td:first-child]:pl-[13px] [&_tr_td:last-child]:pr-[13px] [&_tr]:border-t-2 [&_tr]:border-b-2 [&_tr]:border-table-border">
                {ops &&
                  ops.results.data.map((op) => (
                    <TableData key={op.id} data={op} />
                  ))}
              </tbody>
              <tfoot className="sticky bottom-0 h-10 bg-secondary [&_td]:py-[10px] [&_td]:px-2 [&_td]:leading-5 [&_td]:text-sm [&_td]:whitespace-nowrap [&_td]:text-white [&_td:first-child]:rounded-bl-[18px] [&_td:first-child]:pl-[13px] [&_td:last-child]:rounded-br-[18px]">
                <tr>
                  <td>Sıralanır: {ops.count}</td>
                  <td colSpan={7}></td>
                </tr>
              </tfoot>
            </table>
            : <p className="text-center my-[40px]">Məlumat tapılmadı</p>
        )}
      </div>
      {ops
        && !isLoading
        &&
        <PaginationComponent totalCount={ops.count} />
      }
    </div>
  )
}

export default OperationTable
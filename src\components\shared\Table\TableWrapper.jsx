/* THIS IS UNDER DEVELOPMENT COMPONENT */

import generalTableStyles from "@/styles/shared/generalTableStyles.module.css"
import { useSelector } from "react-redux";
import PaginationComponent from "../Pagination/PaginationComponent";
import { useState } from "react";

function TableWrapper({
    pagination = false,
    totalItemCount,
    initialColumnData
}) {
    const [columnData, setColumnData] = useState(initialColumnData);
    const { isSearchOpen } = useSelector(state => state.layout);

    return (
        <>
            <div className={`${generalTableStyles.tableWrapper} ${isSearchOpen && generalTableStyles.searchOpened}`}>
                <table className={generalTableStyles.table}>
                    <TableHead />
                    <tbody className={generalTableStyles.tbody}>
                        {/* {items.results.data.map(contract => <TableData key={contract.id} contract={contract} />)} */}
                    </tbody>
                    <tfoot className={generalTableStyles.tfoot}>
                        <tr>
                            <td>Sıralanır: {items.count}</td>
                            <td colSpan={4}></td>
                            <td className="whitespace-nowrap text-white">{roundTo2(items.results.total_remaining_debt)}</td>
                            <td colSpan={2}></td>
                        </tr>
                    </tfoot>
                </table>
            </div>
            {pagination && <PaginationComponent totalCount={totalItemCount} />}
        </>
    )
}

export default TableWrapper
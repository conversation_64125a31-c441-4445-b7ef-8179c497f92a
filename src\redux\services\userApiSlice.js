import { createApi } from "@reduxjs/toolkit/query/react";
import { authBaseQueryWithReauth } from "./baseQueryWithReauth";

export const userApiSlice = createApi({
  baseQuery: authBaseQueryWithReauth,
  reducerPath: "userApiSlice",
  tagTypes: ["Users", "SingleUser", "Profile", "Positions", "PositionDetail", "Payrolls"],
  endpoints: (builder) => ({
    getAllCities: builder.query({
      query: () => ({
        url: "/users/cities/",
        method: "GET",
        params: { limit: 1000 },
      }),
    }),
    getUserProfile: builder.query({
      query: () => "/users/me/",
      providesTags: ["Profile"],
    }),
    updateUserProfile: builder.mutation({
      query: (credentials) => ({
        url: "/users/me/",
        method: "PUT",
        body: credentials,
      }),
      invalidatesTags: ["Profile"],
    }),
    getAllUsers: builder.query({
      query: (params) => ({
        url: "/users/",
        method: "GET",
        params,
      }),
      providesTags: ["Users"],
      keepUnusedDataFor: 0,
    }),
    getSingleUsers: builder.query({
      query: (id) => ({
        url: `/users/${id}/`,
        method: "GET",
      }),
      providesTags: ["SingleUser"],
      keepUnusedDataFor: 1,
    }),
    addUser: builder.mutation({
      query: (credentials) => {
        const formData = new FormData();

        for (const key in credentials) {
          formData.append(key, credentials[key]);
        }

        return {
          url: "/users/create/",
          method: "POST",
          body: formData,
        };
      },
      invalidatesTags: ["Users", "SingleUser"],
    }),
    updateUser: builder.mutation({
      query: ({ data, id }) => {
        const formData = new FormData();

        for (const key in data) {
          formData.append(key, data[key]);
        }

        return {
          url: `/users/${id}/`,
          method: "PUT",
          body: formData,
        };
      },
      invalidatesTags: ["Users", "SingleUser"],
    }),
    getAllPositions: builder.query({
      query: (params) => ({
        url: `/users/positions/`,
        method: "GET",
        params,
      }),
      providesTags: ["Positions"],
      keepUnusedDataFor: 0,
    }),
    getSinglePosition: builder.query({
      query: (id) => ({
        url: `/users/positions/${id}/`,
        method: "GET",
      }),
      providesTags: ["PositionDetail"],
      keepUnusedDataFor: 0,
    }),
    addPosition: builder.mutation({
      query: (credentials) => ({
        url: `/users/positions/create/`,
        method: "POST",
        body: credentials,
      }),
      invalidatesTags: ["Positions"],
    }),
    updatePosition: builder.mutation({
      query: ({ data, id }) => ({
        url: `/users/positions/${id}/`,
        method: "PUT",
        body: data,
      }),
      invalidatesTags: ["PositionDetail"],
    }),
    deletePosition: builder.mutation({
      query: ({ data, id }) => ({
        url: `/users/positions/${id}/`,
        method: "DELETE",
        body: data,
      }),
      invalidatesTags: ["Positions"],
    }),
    getAllPayrolls: builder.query({
      query: (params) => ({
        url: `/users/payrolls/`,
        method: "GET",
        params,
      }),
      providesTags: ["Payrolls"],
      keepUnusedDataFor: 0,
    }),
    payPayroll: builder.mutation({
      query: (credentials) => ({
        url: `/users/payrolls/pay/`,
        method: "POST",
        body: credentials,
      }),
      invalidatesTags: ["Payrolls"],
    }),
    logoutUser: builder.mutation({
      query: (credentials) => ({
        url: `/users/logout/`,
        method: "POST",
        body: credentials,
      }),
    }),
  }),
});

export const {
  useGetAllCitiesQuery,
  useGetUserProfileQuery,
  useUpdateUserProfileMutation,
  useGetAllUsersQuery,
  useGetSingleUsersQuery,
  useAddUserMutation,
  useUpdateUserMutation,
  useGetAllPositionsQuery,
  useGetSinglePositionQuery,
  useAddPositionMutation,
  useUpdatePositionMutation,
  useDeletePositionMutation,
  useGetAllPayrollsQuery,
  usePayPayrollMutation,
  useLogoutUserMutation,
} = userApiSlice;

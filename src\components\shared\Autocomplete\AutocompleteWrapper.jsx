import { Autocomplete, CircularProgress, TextField } from "@mui/material";
import { usePaginatedData } from "@/hooks/usePaginatedData";
import { useDispatch, useSelector } from "react-redux";
import { setData } from "@/redux/features/infiniteScrollSlice";
import { useRef, useCallback } from "react";

function AutocompleteWrapper({
    type,
    queryParams = {},
    isOptionalField = false,
    dataConfig
}) {
    const dispatch = useDispatch();
    const observerRef = useRef(null);

    if (!dataConfig) {
        throw new Error('AutocompleteWrapper requires dataConfig prop. Use UserAutocompleteWrapper for user-specific functionality.');
    }

    const { searchQuery, isListOpen, selectedValue, error } =
        useSelector(state => state.infiniteScroll[type]);
    const autocompleteData = useSelector(state => state.infiniteScroll);

    // Use generic paginated data hook
    const { data, isLoading, isFetching, hasMore, loadMore, reset } = usePaginatedData(
        isListOpen,
        searchQuery,
        { ...queryParams, searchField: dataConfig.searchField },
        dataConfig.apiHook
    );

    const lastOptionRef = useCallback(
        node => {
            if (isLoading || isFetching) return;
            if (observerRef.current) observerRef.current.disconnect();
            observerRef.current = new IntersectionObserver(entries => {
                if (entries[0].isIntersecting && hasMore) {
                    loadMore();
                }
            });
            if (node) observerRef.current.observe(node);
        },
        [isLoading, isFetching, hasMore, loadMore]
    );

    // Apply filtering logic if provided
    let filteredItems = data || [];

    if (dataConfig.filterLogic) {
        filteredItems = dataConfig.filterLogic(data || [], autocompleteData, type);
    }

    return (
        <>
            <Autocomplete
                id={`dataSelector-${type}`}
                size="small"
                className="w-full"
                onOpen={() =>
                    dispatch(
                        setData({ mainKey: type, key: "isListOpen", value: true })
                    )
                }
                onClose={() => {
                    dispatch(
                        setData({ mainKey: type, key: "isListOpen", value: false })
                    );
                    reset();
                }}
                options={filteredItems}
                getOptionLabel={option => option[dataConfig.displayField] || ""}
                loading={isLoading}
                isOptionEqualToValue={(option, value) => option[dataConfig.idField] === value?.[dataConfig.idField]}
                onChange={(_, newVal) => {
                    dispatch(
                        setData({ mainKey: type, key: "selectedValue", value: newVal })
                    );
                    dispatch(setData({ mainKey: type, key: "error", value: false }));
                }}
                value={selectedValue}
                onInputChange={(_, newInputValue) =>
                    dispatch(
                        setData({ mainKey: type, key: "searchQuery", value: newInputValue })
                    )
                }
                renderOption={(props, option, state) => {
                    const isLast = state.index === filteredItems.length - 1;
                    return (
                        <li
                            {...props}
                            key={option[dataConfig.idField]}
                            ref={isLast ? lastOptionRef : undefined}
                        >
                            {option[dataConfig.displayField]}
                            {isLast && (isLoading || isFetching) && <em> {dataConfig.loadingText}</em>}
                        </li>
                    );
                }}
                renderInput={params => (
                    <TextField
                        {...params}
                        variant="standard"
                        error={!isOptionalField && error}
                        helperText={
                            !isOptionalField && error ? "Bu hissə boş ola bilməz" : ""
                        }
                        InputProps={{
                            ...params.InputProps,
                            endAdornment: (
                                <>
                                    {(isLoading || isFetching) && (
                                        <CircularProgress size={20} />
                                    )}
                                    {params.InputProps.endAdornment}
                                </>
                            )
                        }}
                    />
                )}
            />
        </>
    );
}

export default AutocompleteWrapper;